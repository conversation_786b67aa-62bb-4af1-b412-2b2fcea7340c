/*
import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2196F3);
  static const Color secondary = Color(0xFF03A9F4);
  static const Color error = Color(0xFFE53935);

  // Light Theme Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);

  // Dark Theme Colors
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color textPrimaryDark = Color(0xFFE0E0E0);
  static const Color textSecondaryDark = Color(0xFF9E9E9E);

  // Additional Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFFC107);
  static const Color info = Color(0xFF00BCD4);
  static const Color divider = Color(0xFFBDBDBD);
  static const Color border = Color(0xFFE0E0E0);
  
  // Light Colors (from utils/constants/colors.dart)
  static const Color light = Color(0xFFFFFFFF);
  static const Color lightFillColor = Color(0xFFF4F4F7);
  static const Color lightIconsColor = Color(0xFF929AA5);
  static const Color lightTitleColor = Color(0xFF252525);
  static const Color lightBorderColor = Color(0xFFEAECEF);
  static const Color lightHintTextColor = Color(0xFF636363);

  // Dark Colors (from utils/constants/colors.dart)
  static const Color dark = Color(0xFF1B1B1C);
  static const Color darkFillColor = Color(0xFF242426);
  static const Color darkIconsColor = Color(0xFF7C808D);
  static const Color darkTitleColor = Color(0xFFEAEAEA);
  static const Color darkBorderColor = Color(0xFF333B47);
  static const Color darkHintTextColor = Color(0xFF848E9C);
  
  // Additional Colors (from utils/constants/colors.dart)
  static const Color accent = Color(0xFFB0C7FF);
  static const Color greyColor = Color(0xFF9E9E9E);
  static const Color greyer = Color(0xFF434C5A);
}

*/