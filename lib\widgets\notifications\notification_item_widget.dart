import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import '../../core/controllers/notifications_controller.dart';
import '../../core/models/notification_model.dart';

/// Widget for displaying a notification item
class NotificationItemWidget extends StatelessWidget {
  /// The notification to display
  final NotificationModel notification;

  /// The controller
  final NotificationsController controller;

  /// Constructor
  const NotificationItemWidget({
    super.key,
    required this.notification,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor:
            notification.isRead
                ? Colors.grey
                : Theme.of(context).colorScheme.primary,
        child: Icon(notification.getIcon(), color: Colors.white),
      ),
      title: Text(notification.title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(notification.message),
          const SizedBox(height: 4),
          Text(
            notification.getFormattedTime(),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
      trailing:
          notification.isRead
              ? null
              : IconButton(
                icon: const Icon(Icons.circle, size: 12),
                onPressed: () async {
                  await controller.markAsRead(notification.id);
                },
              ),
      onTap: () async {
        await controller.markAsRead(notification.id);

        // Handle notification tap based on type
        if (notification.type == 'transaction' &&
            notification.referenceId != null) {
          // Navigate to transaction details
          // ignore: use_build_context_synchronously

          Get.toNamed(
            '/transaction-details',
            arguments: notification.referenceId,
          );
        }
      },
    );
  }
}
