import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../common/widgets/text form field/costume_text_field.dart';
import '../../core/providers/user_provider.dart';
import '../transfer_status_screen.dart';

class PaymentController extends GetxController {
  static PaymentController get instance => Get.find();

  // Get user controller to access contacts
  final UserController _userController = Get.find<UserController>();

  // List to store contacts from data.json
  final RxList<Contact> _contacts = <Contact>[].obs;

  Rx<Contact?> selectedContact = Rx<Contact?>(null);
  RxString searchQuery = ''.obs;
  RxString contactInputText = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _loadContactsFromUserData();
  }

  // Load contacts from user data
  void _loadContactsFromUserData() {
    // Clear existing contacts
    _contacts.clear();

    // Get current user
    final currentUser = _userController.currentUser;

    if (currentUser != null && currentUser.user.contacts.isNotEmpty) {
      // Convert app_models.Contact to our Contact model
      for (final contact in currentUser.user.contacts) {
        _contacts.add(Contact(contact.name, contact.account));
      }
    } else {
      // Add some default contacts if no contacts found
      _contacts.addAll([
        Contact('Mazen Idris', '*********'),
        Contact('Mohammed Adam', '*********'),
      ]);
    }
  }

  void toggleSelection(Contact contact) {
    selectedContact.value = selectedContact.value == contact ? null : contact;
    contactInputText.value = selectedContact.value?.name ?? '';
  }

  List<Contact> get filteredContacts {
    if (searchQuery.value.isEmpty) {
      return _contacts;
    }
    return _contacts
        .where(
          (contact) =>
              contact.name.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              ) ||
              contact.account.contains(searchQuery.value),
        )
        .toList();
  }
}

class Payment extends StatelessWidget {
  const Payment({super.key, this.amount = '0'});

  final String amount;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PaymentController());
    return Scaffold(
      appBar: BuildAppBar(
        leadingIcon: Icons.close,
        leadingOnPressed: () => Navigator.of(context).pop(),
        title: Center(
          child: Text(
            '$amount \$',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
        ),
        action: [
          SizedBox(
            width: 100,
            child: ElevatedButton(
              onPressed:
                  () => Get.to(
                    () => TransferStatusScreen.create(
                      amount: amount,
                      currency: 'USD',
                    ),
                  ),
              child: const Text('Continue'),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => CostumeTextField(
                prefixText: 'To: ',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                onChanged: (value) => controller.searchQuery.value = value,
                controller: TextEditingController(
                  text: controller.contactInputText.value,
                ),
                hintText: 'Enter name or account number',
                filled: true,
              ),
            ),
            Expanded(
              child: Obx(
                () => ListView(
                  children: [
                    ContactSection(
                      title: 'CONTACTS',
                      contacts: controller.filteredContacts,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ContactSection extends StatelessWidget {
  const ContactSection({
    super.key,
    required this.title,
    required this.contacts,
  });

  final String title;
  final List<Contact> contacts;

  @override
  Widget build(BuildContext context) {
    final PaymentController controller = Get.find<PaymentController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            title,
            style: const TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ),
        Column(
          children:
              contacts.map((contact) {
                return Obx(
                  () => ContactTile(
                    contact: contact,
                    isSelected: controller.selectedContact.value == contact,
                    onTap: controller.toggleSelection,
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}

class Contact {
  final String name;
  final String account;

  Contact(this.name, this.account);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Contact &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          account == other.account;

  @override
  int get hashCode => name.hashCode ^ account.hashCode;
}

class ContactTile extends StatelessWidget {
  const ContactTile({
    super.key,
    required this.contact,
    required this.isSelected,
    required this.onTap,
  });

  final Contact contact;
  final bool isSelected;
  final Function(Contact) onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(contact),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
        leading: CircleAvatar(
          radius: 24,
          backgroundColor: Colors.grey.shade200,
          child: const Icon(Icons.person, color: Colors.grey),
        ),
        title: Text(
          contact.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(contact.account),
        trailing:
            isSelected
                ? Icon(Icons.check_circle, color: Colors.grey[400])
                : null,
      ),
    );
  }
}
