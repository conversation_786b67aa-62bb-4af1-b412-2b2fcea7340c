import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller responsible for managing wallet top-up functionality
class TopUpWalletController extends GetxController {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _selectedPaymentMethod = 'credit_card'.obs;
  final _isProcessing = false.obs;
  
  final List<Map<String, dynamic>> _paymentMethods = [
     {
      'id': 'bank_transfer',
      'name': 'Bank Transfer',
      'icon': Icons.account_balance,
      'description': 'Direct bank transfer',
    },
    {
      'id': 'credit_card',
      'name': 'Credit Card',
      'icon': Icons.credit_card,
      'description': 'Visa, Mastercard, American Express',
    }
  ];

  final List<double> _quickAmounts = [10.0, 25.0, 50.0, 100.0, 250.0, 500.0];

  @override
  void onClose() {
    _amountController.dispose();
    super.onClose();
  }

  /// Process the wallet top-up
  void processTopUp() {
    if (_formKey.currentState!.validate()) {
      _isProcessing.value = true;

      // Simulate processing
      Future.delayed(const Duration(seconds: 2), () {
        _isProcessing.value = false;

        // Navigate to transaction details
        Get.toNamed(
          '/transaction-details',
          arguments: {
            'recipient': 'Wallet Top-Up',
            'amount': double.parse(_amountController.text),
            'transactionType': 'Top-Up',
            'transactionDate': DateTime.now(),
            'note': 'Top-up via ${_getSelectedPaymentMethodName()}',
            'status': 'Completed',
          },
        );
      });
    }
  }

  /// Get the name of the selected payment method
  String _getSelectedPaymentMethodName() {
    final method = _paymentMethods.firstWhere(
      (method) => method['id'] == _selectedPaymentMethod.value,
      orElse: () => {'name': 'Unknown'},
    );
    return method['name'];
  }

  /// Set amount from quick amount options
  void setQuickAmount(double amount) {
    _amountController.text = amount.toString();
  }

  // Getters
  GlobalKey<FormState> get formKey => _formKey;
  TextEditingController get amountController => _amountController;
  RxString get selectedPaymentMethod => _selectedPaymentMethod;
  RxBool get isProcessing => _isProcessing;
  List<Map<String, dynamic>> get paymentMethods => _paymentMethods;
  List<double> get quickAmounts => _quickAmounts;
}
