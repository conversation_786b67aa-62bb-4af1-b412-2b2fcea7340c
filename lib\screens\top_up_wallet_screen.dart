import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/top_up_wallet_controller.dart';

class TopUpWalletScreen extends StatelessWidget {
  const TopUpWalletScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TopUpWalletController>();

    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Top-Up Wallet'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Amount Input
              const Text(
                'Enter Amount',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: controller.amountController,
                decoration: InputDecoration(
                  prefixText: '\$',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  hintText: '0.00',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  if (amount > 10000) {
                    return 'Maximum top-up amount is \$10,000';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Payment Methods
              const Text(
                'Payment Method',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Obx(
                () => Column(
                  children:
                      controller.paymentMethods.map((method) {
                        final isSelected =
                            method['id'] ==
                            controller.selectedPaymentMethod.value;
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color:
                                  isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.transparent,
                              width: 2,
                            ),
                          ),
                          child: RadioListTile<String>(
                            title: Row(
                              children: [
                                Icon(method['icon']),
                                const SizedBox(width: 12),
                                Text(method['name']),
                              ],
                            ),
                            subtitle: Text(method['description']),
                            value: method['id'],
                            groupValue: controller.selectedPaymentMethod.value,
                            onChanged: (value) {
                              controller.selectedPaymentMethod.value = value!;
                            },
                            activeColor: Theme.of(context).colorScheme.primary,
                          ),
                        );
                      }).toList(),
                ),
              ),
              const SizedBox(height: 24),

              // Top-Up Button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed:
                        controller.isProcessing.value
                            ? null
                            : controller.processTopUp,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        controller.isProcessing.value
                            ? const CircularProgressIndicator()
                            : const Text(
                              'Top-Up Now',
                              style: TextStyle(fontSize: 16),
                            ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Quick Amounts
              const Text(
                'Quick Amounts',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    controller.quickAmounts
                        .map(
                          (amount) => ActionChip(
                            label: Text('\$${amount.toStringAsFixed(0)}'),
                            onPressed: () {
                              controller.setQuickAmount(amount);
                            },
                          ),
                        )
                        .toList(),
              ),
              const SizedBox(height: 16),

              // Fee Information
              Card(
                margin: const EdgeInsets.only(top: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Important Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow('Processing Time', 'Instant', Icons.timer),
                      _buildInfoRow(
                        'Fees',
                        'No fees for top-ups',
                        Icons.info_outline,
                      ),
                      _buildInfoRow(
                        'Limits',
                        'Min: \$5, Max: \$10,000',
                        Icons.lock_outline,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          Text(value),
        ],
      ),
    );
  }
}
