import 'package:flutter/material.dart';
import '../../core/controllers/transaction_details_controller.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// Widget for displaying the transaction header including status and amount
class TransactionHeaderWidget extends StatelessWidget {
  /// The controller
  final TransactionDetailsController controller;

  /// Constructor
  const TransactionHeaderWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppSizes.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusSection(),
            Divider(height: AppSizes.lg),
            _buildAmountSection(),
          ],
        ),
      ),
    );
  }

  /// Build the status section
  Widget _buildStatusSection() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(AppSizes.sm),
          decoration: BoxDecoration(
            color: controller.getStatusColor().withAl<PERSON>(30),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check_circle,
            color: controller.getStatusColor(),
            size: 24,
          ),
        ),
        SizedBox(width: AppSizes.md),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة المعاملة',
              style: TextStyle(
                fontSize: AppSizes.bodySmall,
                color: AppColors.greyColor,
              ),
            ),
            Text(
              controller.status,
              style: TextStyle(
                fontSize: AppSizes.titleMedium,
                fontWeight: FontWeight.bold,
                color: controller.getStatusColor(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the amount section
  Widget _buildAmountSection() {
    final isReceive = controller.transactionType.toLowerCase() == 'receive';
    final color = isReceive ? AppColors.success : AppColors.error;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المبلغ',
          style: TextStyle(
            fontSize: AppSizes.bodySmall,
            color: AppColors.greyColor,
          ),
        ),
        SizedBox(height: AppSizes.sm),
        Row(
          children: [
            Icon(
              controller.getTransactionTypeIcon(),
              color: color,
              size: 24,
            ),
            SizedBox(width: AppSizes.sm),
            Text(
              controller.getFormattedAmount(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: AppSizes.sm),
        Text(
          isReceive ? 'تم استلام المبلغ' : 'تم إرسال المبلغ',
          style: TextStyle(
            fontSize: AppSizes.bodySmall,
            color: color,
          ),
        ),
      ],
    );
  }
}
