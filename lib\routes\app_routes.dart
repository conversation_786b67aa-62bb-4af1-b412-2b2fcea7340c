import 'package:get/get.dart';
import '../screens/splash_screen.dart';
import '../screens/home_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/account_settings_screen.dart';
import '../screens/receive_money_screen.dart';
import '../screens/withdraw_funds_screen.dart';
import '../screens/contacts_screen.dart';
import '../screens/currency_exchange_screen.dart';
import '../screens/notifications_screen.dart';
import '../screens/login_screen.dart';
import '../screens/register_screen.dart';
import '../screens/otp_verification_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/transaction_history_screen.dart';
import '../screens/send_money_screen.dart';
import '../screens/transaction_details_screen.dart';
import '../screens/help_support_screen.dart';
import '../screens/top_up_wallet_screen.dart';
import '../screens/account_screen.dart';
import '../core/controllers/receive_money_controller.dart';
import '../core/controllers/currency_exchange_controller.dart';
import '../core/controllers/contacts_controller.dart';
import '../../features/auth/presentation/controllers/auth_controller.dart';
import '../core/controllers/register_controller.dart';
import '../core/controllers/home_controller.dart';
import '../core/controllers/profile_controller.dart';
import '../core/controllers/send_money_controller.dart';
import '../core/controllers/account_controller.dart';
import '../core/providers/user_provider.dart';
import '../core/controllers/notifications_controller.dart';
import '../core/controllers/otp_verification_controller.dart';
import '../core/controllers/top_up_wallet_controller.dart';
import '../core/controllers/withdraw_funds_controller.dart';
import '../core/controllers/splash_controller.dart';
import '../core/controllers/credit_cards_controller.dart';
import '../core/controllers/bills_controller.dart';
import '../screens/credit_cards_screen.dart';
import '../screens/bills_screen.dart';
import '../screens/about_screen.dart';
import '../screens/kyc/kyc.dart';
import '../screens/transfer/transfer.dart';
import '../screens/pay/payment.dart';
import '../screens/modify_pin_code/modify_pin_code.dart';
import '../screens/bind_phone_number/bind_phone_number.dart';
import '../screens/bind_email/bind_email.dart';

class AppRoutes {
  static const String account = '/account';
  static const String splash = '/splash';
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String home = '/home';
  static const String profile = '/profile';
  static const String accountSettings = '/account-settings';
  static const String settings = '/settings';
  static const String receiveMoney = '/receive-money';
  static const String sendMoney = '/send-money';
  static const String withdrawFunds = '/withdraw-funds';
  static const String topUpWallet = '/top-up-wallet';
  static const String contacts = '/contacts';
  static const String rounds = '/rounds';
  static const String currencyExchange = '/currency-exchange';
  static const String notifications = '/notifications';
  static const String transactionHistory = '/transaction-history';
  static const String transactionDetails = '/transaction-details';
  static const String helpSupport = '/help-support';
  static const String creditCards = '/credit-cards';
  static const String bills = '/bills';
  static const String about = '/about';
  static const String kyc = '/kyc';
  static const String transfer = '/transfer';
  static const String payment = '/payment';
  static const String modifyPinCode = '/modify-pin-code';
  static const String bindPhoneNumber = '/bind-phone-number';
  static const String bindEmail = '/bind-email';

  static List<GetPage> getPages() {
    return [
      GetPage(
        name: splash,
        page: () => const SplashScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<SplashController>(() => SplashController());
        }),
      ),
      GetPage(
        name: login,
        page: () => const LoginScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<AuthController>(() => AuthController());
        }),
      ),
      GetPage(
        name: home,
        page: () => const HomeScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<HomeController>(() => HomeController());
        }),
      ),
      GetPage(
        name: account,
        page: () => const AccountScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<AccountController>(() => AccountController());
        }),
      ),
      GetPage(
        name: profile,
        page: () => const ProfileScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<ProfileController>(() => ProfileController());
        }),
      ),
      GetPage(name: settings, page: () => const SettingsScreen()),
      GetPage(
        name: accountSettings,
        page: () => const AccountSettingsScreen(),
        binding: BindingsBuilder(() {
          // Ensure UserController is available
          if (!Get.isRegistered<UserController>()) {
            Get.put(UserController(), permanent: true);
          }
        }),
      ),
      GetPage(name: helpSupport, page: () => const HelpSupportScreen()),
      GetPage(
        name: register,
        page: () => const RegisterScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<RegisterController>(() => RegisterController());
        }),
      ),
      GetPage(
        name: otpVerification,
        page:
            () => OTPVerificationScreen(
              email: Get.arguments['email'] ?? '',
              phone: Get.arguments['phone'] ?? '',
            ),
        binding: BindingsBuilder(() {
          Get.lazyPut<OTPVerificationController>(
            () => OTPVerificationController(
              email: Get.arguments['email'],
              phone: Get.arguments['phone'],
            ),
          );
        }),
      ),
      GetPage(
        name: sendMoney,
        page: () => const SendMoneyScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<SendMoneyController>(() => SendMoneyController());
        }),
      ),
      GetPage(
        name: receiveMoney,
        page: () => const ReceiveMoneyScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<ReceiveMoneyController>(() => ReceiveMoneyController());
        }),
      ),
      GetPage(
        name: withdrawFunds,
        page: () => const WithdrawFundsScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<WithdrawFundsController>(() => WithdrawFundsController());
        }),
      ),
      GetPage(
        name: contacts,
        page: () => const ContactsScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<ContactsController>(() => ContactsController());
        }),
      ),
      GetPage(
        name: transactionHistory,
        page: () => TransactionHistoryScreen.create(),
      ),
      GetPage(
        name: transactionDetails,
        page: () => const TransactionDetailsScreen(),
      ),
      GetPage(
        name: currencyExchange,
        page: () => const CurrencyExchangeScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<CurrencyExchangeController>(
            () => CurrencyExchangeController(),
          );
        }),
      ),
      GetPage(
        name: notifications,
        page: () => const NotificationsScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<NotificationsController>(() => NotificationsController());
        }),
      ),
      GetPage(
        name: topUpWallet,
        page: () => const TopUpWalletScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<TopUpWalletController>(() => TopUpWalletController());
        }),
      ),
      GetPage(
        name: creditCards,
        page: () => const CreditCardsScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<CreditCardsController>(() => CreditCardsController());
        }),
      ),
      GetPage(
        name: bills,
        page: () => const BillsScreen(),
        binding: BindingsBuilder(() {
          Get.lazyPut<BillsController>(() => BillsController());
        }),
      ),
      GetPage(name: about, page: () => const AboutScreen()),
      GetPage(name: kyc, page: () => const KYC()),
      GetPage(name: transfer, page: () => const Transfer()),
      GetPage(
        name: payment,
        page: () {
          final args = Get.arguments;
          final amount =
              args != null && args['amount'] != null
                  ? args['amount'].toString()
                  : '0';
          return Payment(amount: amount);
        },
      ),
      GetPage(name: modifyPinCode, page: () => const ModifyPinCode()),
      GetPage(name: bindPhoneNumber, page: () => const BindPhoneNumber()),
      GetPage(name: bindEmail, page: () => const BindEmail()),
    ];
  }
}
