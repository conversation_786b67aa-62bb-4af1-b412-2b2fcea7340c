import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller responsible for managing OTP verification
class OTPVerificationController extends GetxController {
  final String? email;
  final String? phone;
  
  final _otpControllers = List.generate(6, (index) => TextEditingController());
  final _isLoading = false.obs;
  final _timer = 60.obs;
  final _canResend = false.obs;

  OTPVerificationController({this.email, this.phone});

  @override
  void onInit() {
    super.onInit();
    startTimer();
  }

  @override
  void onClose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    super.onClose();
  }

  /// Start countdown timer for OTP resend
  void startTimer() {
    _timer.value = 60;
    _canResend.value = false;
    Future.delayed(const Duration(seconds: 1), () {
      if (_timer.value > 0) {
        _timer.value--;
        startTimer();
      } else {
        _canResend.value = true;
      }
    });
  }

  /// Verify the entered OTP
  void verifyOTP() {
    final otp = _otpControllers.map((c) => c.text).join();
    if (otp.length != 6) {
      Get.snackbar(
        'Error',
        'Please enter a valid OTP',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    _isLoading.value = true;
    // Simulate verification
    Future.delayed(const Duration(seconds: 2), () {
      _isLoading.value = false;
      // Navigate to home screen on success
      Get.offAllNamed('/home');
    });
  }

  /// Resend OTP code
  void resendOTP() {
    if (!_canResend.value) return;

    _canResend.value = false;
    startTimer();
    // Simulate resending OTP
    Get.snackbar(
      'Success',
      'OTP has been resent',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Getters
  List<TextEditingController> get otpControllers => _otpControllers;
  RxBool get isLoading => _isLoading;
  RxInt get timer => _timer;
  RxBool get canResend => _canResend;
}
