import 'package:get/get.dart';
import '../models/notification_model.dart';
import '../models/user_model.dart';
import '../providers/user_provider.dart';

/// Service responsible for managing notifications
class NotificationService extends GetxService {
  // Private variables
  final RxList<NotificationModel> _notifications = <NotificationModel>[].obs;
  final RxInt _unreadCount = 0.obs;

  // User controller reference
  late final UserController _userController;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  int get unreadCount => _unreadCount.value;

  @override
  void onInit() {
    super.onInit();

    // Try to find UserController
    try {
      _userController = Get.find<UserController>();
      _initializeNotifications();
      _setupListeners();
    } catch (e) {
      Get.log('UserController not found in NotificationService', isError: true);
      // We'll initialize later when UserController becomes available

      // Schedule a retry
      Future.delayed(const Duration(milliseconds: 500), () {
        try {
          _userController = Get.find<UserController>();
          _initializeNotifications();
          _setupListeners();
        } catch (e) {
          Get.log(
            'UserController still not found in NotificationService',
            isError: true,
          );
        }
      });
    }
  }

  /// Initialize notifications from user data
  void _initializeNotifications() {
    if (_userController.isLoggedIn) {
      _loadNotificationsFromUserData();
    }
  }

  /// Setup listeners for user events
  void _setupListeners() {
    // Listen for user login/logout
    ever(_userController.currentUserRx, (user) {
      if (user != null) {
        _loadNotificationsFromUserData();
      } else {
        _notifications.clear();
        _updateUnreadCount();
      }
    });

    // Listen for transactions
    _userController.transactionsStream.listen((transactions) {
      _generateTransactionNotifications(transactions);
    });

    // Listen for account changes
    _userController.accountStream.listen((account) {
      if (account != null) {
        _generateAccountNotifications(account);
      }
    });

    // Listen for security events
    _userController.securityStream.listen((security) {
      _generateSecurityNotifications(security);
    });
  }

  /// Load notifications from user data
  void _loadNotificationsFromUserData() {
    try {
      final user = _userController.currentUser;
      if (user != null) {
        // Get notifications from user data
        final notificationsData = user.user.notifications.data;

        if (notificationsData.containsKey('items')) {
          final items = notificationsData['items'] as List;
          final notificationsList =
              items
                  .map(
                    (item) => NotificationModel.fromJson(
                      item as Map<String, dynamic>,
                    ),
                  )
                  .toList();

          // Sort notifications by timestamp (newest first)
          notificationsList.sort((a, b) => b.timestamp.compareTo(a.timestamp));

          _notifications.value = notificationsList;
          _updateUnreadCount();
        } else {
          // No notifications found, generate initial notifications
          _generateInitialNotifications();
        }
      }
    } catch (e) {
      Get.log('Error loading notifications from user data: $e', isError: true);
      // Generate initial notifications as fallback
      _generateInitialNotifications();
    }
  }

  /// Generate initial notifications for new users
  void _generateInitialNotifications() {
    final now = DateTime.now();
    final List<NotificationModel> initialNotifications = [];

    // Welcome notification
    initialNotifications.add(
      NotificationModel(
        id: 'WELCOME${now.millisecondsSinceEpoch}',
        title: 'مرحباً بك في تطبيق المحفظة',
        message:
            'شكراً لاستخدامك تطبيقنا. استكشف الميزات المتاحة واستمتع بتجربة مصرفية سلسة.',
        timestamp: now,
        type: 'system',
        isRead: false,
      ),
    );

    // Account created notification
    initialNotifications.add(
      NotificationModel(
        id: 'ACCOUNT${now.millisecondsSinceEpoch}',
        title: 'تم إنشاء حسابك بنجاح',
        message:
            'تم إنشاء حسابك بنجاح. يمكنك الآن إرسال واستقبال الأموال وإدارة معاملاتك.',
        timestamp: now.subtract(const Duration(minutes: 5)),
        type: 'account',
        isRead: false,
      ),
    );

    _notifications.value = initialNotifications;
    _updateUnreadCount();

    // Save initial notifications to user data
    _saveNotificationsToUserData();
  }

  /// Generate notifications for transactions
  void _generateTransactionNotifications(List<Transaction> transactions) {
    if (transactions.isEmpty) return;

    // Get existing transaction notification IDs
    final existingIds =
        _notifications
            .where((n) => n.type == 'transaction')
            .map((n) => n.referenceId)
            .toList();

    // Find new transactions
    final newTransactions =
        transactions.where((t) {
          // Check if this transaction already has a notification
          return !existingIds.contains(t.id) &&
              !existingIds.contains(t.reference);
        }).toList();

    if (newTransactions.isEmpty) return;

    // Create notifications for new transactions
    for (final transaction in newTransactions) {
      final notification = _createTransactionNotification(transaction);
      if (notification != null) {
        _notifications.add(notification);
      }
    }

    // Sort notifications by timestamp (newest first)
    _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    _updateUnreadCount();
    _saveNotificationsToUserData();
  }

  /// Create a notification for a transaction
  NotificationModel? _createTransactionNotification(Transaction transaction) {
    try {
      String title;
      String message;

      switch (transaction.type.toLowerCase()) {
        case 'sent':
          title = 'تم إرسال الأموال';
          message =
              'تم إرسال ${transaction.amount} ${transaction.currency} إلى ${transaction.toAccount ?? 'حساب آخر'}';
          break;
        case 'received':
          title = 'تم استلام الأموال';
          message =
              'تم استلام ${transaction.amount} ${transaction.currency} من ${transaction.fromAccount ?? 'حساب آخر'}';
          break;
        case 'deposit':
          title = 'تم إيداع الأموال';
          message =
              'تم إيداع ${transaction.amount} ${transaction.currency} في حسابك';
          break;
        case 'withdrawal':
          title = 'تم سحب الأموال';
          message =
              'تم سحب ${transaction.amount} ${transaction.currency} من حسابك';
          break;
        default:
          title = 'معاملة جديدة';
          message =
              'تمت معاملة بقيمة ${transaction.amount} ${transaction.currency}';
      }

      return NotificationModel(
        id: 'TXN${transaction.id}',
        title: title,
        message: message,
        timestamp: transaction.timestamp,
        type: 'transaction',
        isRead: false,
        referenceId: transaction.id,
      );
    } catch (e) {
      Get.log('Error creating transaction notification: $e', isError: true);
      return null;
    }
  }

  /// Generate notifications for account changes
  void _generateAccountNotifications(Account account) {
    // Check for balance changes
    final currentBalance = account.balance;
    final previousBalance = _getPreviousBalance();

    if (previousBalance != null && currentBalance != previousBalance) {
      final notification = NotificationModel(
        id: 'BALANCE${DateTime.now().millisecondsSinceEpoch}',
        title: 'تغيير في الرصيد',
        message:
            'تم تغيير رصيد حسابك من $previousBalance إلى $currentBalance ${account.currency}',
        timestamp: DateTime.now(),
        type: 'account',
        isRead: false,
      );

      _notifications.add(notification);
      _updateUnreadCount();
      _saveNotificationsToUserData();
    }
  }

  /// Get previous balance from user data
  double? _getPreviousBalance() {
    try {
      final user = _userController.currentUser;
      if (user != null && user.user.account != null) {
        return user.user.account!.balance;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Generate notifications for security events
  void _generateSecurityNotifications(Security security) {
    // Check for new devices
    final devices = security.devices;
    if (devices.isNotEmpty) {
      final latestDevice = devices.last;

      // Check if we already have a notification for this device
      final existingDeviceNotification = _notifications.any(
        (n) => n.type == 'security' && n.referenceId == latestDevice.id,
      );

      if (!existingDeviceNotification) {
        final notification = NotificationModel(
          id: 'DEVICE${DateTime.now().millisecondsSinceEpoch}',
          title: 'جهاز جديد',
          message: 'تم تسجيل الدخول من جهاز جديد: ${latestDevice.name}',
          timestamp: DateTime.now(),
          type: 'security',
          isRead: false,
          referenceId: latestDevice.id,
        );

        _notifications.add(notification);
        _updateUnreadCount();
        _saveNotificationsToUserData();
      }
    }
  }

  /// Update the unread count
  void _updateUnreadCount() {
    _unreadCount.value = _notifications.where((n) => !n.isRead).length;
  }

  /// Save notifications to user data
  void _saveNotificationsToUserData() {
    try {
      final user = _userController.currentUser;
      if (user != null) {
        // Convert notifications to JSON
        final notificationsJson =
            _notifications.map((n) => n.toJson()).toList();

        // Create updated notifications data
        final updatedNotificationsData = {
          'push': user.user.notifications.push,
          'sms': user.user.notifications.sms,
          'email': user.user.notifications.email,
          'items': notificationsJson,
        };

        // Update user data
        _userController.updateUserNotifications(updatedNotificationsData);
      }
    } catch (e) {
      Get.log('Error saving notifications to user data: $e', isError: true);
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    final index = _notifications.indexWhere((n) => n.id == id);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index].isRead = true;
      _notifications.refresh();
      _updateUnreadCount();

      // Save changes to user data
      _saveNotificationsToUserData();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    bool anyUnread = false;

    for (var notification in _notifications) {
      if (!notification.isRead) {
        notification.isRead = true;
        anyUnread = true;
      }
    }

    if (anyUnread) {
      _notifications.refresh();
      _updateUnreadCount();

      // Save changes to user data
      _saveNotificationsToUserData();
    }
  }

  /// Add a custom notification
  void addNotification(NotificationModel notification) {
    _notifications.add(notification);
    _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    _updateUnreadCount();
    _saveNotificationsToUserData();
  }
}
