import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../common/widgets/appbar/custom_appbar.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(showBackArrow: true, title: const Text('About')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // App Logo
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 60,
              ),
            ),
            const SizedBox(height: 24),

            // App Name
            Text(
              'OWallet',
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // App Version
            Text(
              'Version 1.0.0 (Build 100)',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 32),

            // App Description
            const Text(
              'OWallet is a digital wallet application that helps you manage your finances, track expenses, and make secure transactions.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),

            // Developer Info
            _buildSectionTitle(context, 'Developed By'),
            const SizedBox(height: 16),
            const Text(
              'DCD Development Team',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => _launchUrl('mailto:<EMAIL>'),
              child: const Text('<EMAIL>'),
            ),
            const SizedBox(height: 24),

            // Technologies Used
            _buildSectionTitle(context, 'Technologies'),
            const SizedBox(height: 16),
            _buildTechnologyItem(context, 'Flutter', 'UI Framework'),
            _buildTechnologyItem(context, 'Dart', 'Programming Language'),
            _buildTechnologyItem(context, 'GetX', 'State Management'),
            const SizedBox(height: 24),

            // Links
            _buildSectionTitle(context, 'Links'),
            const SizedBox(height: 16),
            _buildLinkButton(
              context,
              'Website',
              Icons.language,
              () => _launchUrl('https://owallet.com'),
            ),
            _buildLinkButton(
              context,
              'Privacy Policy',
              Icons.privacy_tip,
              () => _launchUrl('https://owallet.com/privacy'),
            ),
            _buildLinkButton(
              context,
              'Terms of Service',
              Icons.description,
              () => _launchUrl('https://owallet.com/terms'),
            ),
            const SizedBox(height: 32),

            // Copyright
            Text(
              '© ${DateTime.now().year} DCD Development. All rights reserved.',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildTechnologyItem(
    BuildContext context,
    String name,
    String description,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            name,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(width: 8),
          Text('- $description', style: const TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  Widget _buildLinkButton(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(title),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
