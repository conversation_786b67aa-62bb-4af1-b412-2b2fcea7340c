import 'package:flutter/material.dart';

import '../../constants/colors.dart';
import '../../constants/sizes.dart';

class CustomElevatedButtonTheme {
  /// --To Avoid Creating Instances
  CustomElevatedButtonTheme._();

  /// -- Light Theme
  static final light = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 0.0,
      shadowColor: Colors.transparent,
      foregroundColor: AppColors.lightTitleColor,
      backgroundColor: AppColors.lightFillColor,
      disabledForegroundColor: AppColors.greyColor,
      disabledBackgroundColor: AppColors.greyColor,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      side: const BorderSide(color: AppColors.lightFillColor, width: 1.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      textStyle: const TextStyle(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor),
    ),
  );

  /// -- Light Theme
  static final dark = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 0.0,
      shadowColor: Colors.transparent,
      foregroundColor: AppColors.darkTitleColor,
      backgroundColor: AppColors.darkFillColor,
      disabledForegroundColor: AppColors.greyColor,
      disabledBackgroundColor: AppColors.greyColor,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      side: const BorderSide(color: AppColors.darkFillColor, width: 1.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      textStyle: const TextStyle(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor),
    ),
  );
}
