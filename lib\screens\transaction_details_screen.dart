import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/transaction_details_controller.dart';
import '../widgets/transaction_details_widgets.dart';

/// Screen for displaying transaction details
class TransactionDetailsScreen extends StatelessWidget {
  const TransactionDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TransactionDetailsController());

    return Scaffold(
      appBar: BuildAppBar(showBackArrow: true, title: const Text('Details')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16.0),
            buildAmountCard(controller),
            const SizedBox(height: 16.0),
            buildDetails<PERSON><PERSON>(controller),
            const Sized<PERSON>ox(height: 16.0),
            buildNote<PERSON>ard(controller),
          ],
        ),
      ),
    );
  }
}
