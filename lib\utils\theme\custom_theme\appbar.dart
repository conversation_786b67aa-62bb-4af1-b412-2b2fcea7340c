import 'package:flutter/material.dart';
import '../../constants/colors.dart';
import '../../constants/sizes.dart';

class CustomAppBarTheme {
  CustomAppBarTheme._();

  static const light = AppBarTheme(
    elevation: 0.0,
    centerTitle: false,
    scrolledUnderElevation: 0.0,
    backgroundColor: Colors.transparent,
    surfaceTintColor: Colors.transparent,
    //iconTheme: IconThemeData(color: AppColors.lightIconsColor, size: 24),
    actionsIconTheme: IconThemeData(color: AppColors.lightIconsColor, size: 24),
    titleTextStyle: TextStyle(fontSize: AppSizes.titleMedium, color: AppColors.lightTitleColor)
  );

  static const dark= AppBarTheme(
      elevation: 0.0,
      centerTitle: false,
      scrolledUnderElevation: 0.0,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      //iconTheme: IconThemeData(color: AppColors.darkIconsColor, size: 24),
      actionsIconTheme: IconThemeData( color: AppColors.darkIconsColor, size: 24),
      titleTextStyle: TextStyle(fontSize: AppSizes.titleMedium, color: AppColors.darkTitleColor)
  );
}
