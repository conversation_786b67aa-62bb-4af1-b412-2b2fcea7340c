import 'package:flutter/material.dart';
import 'security_tip_widget.dart';

/// Widget for displaying a card with security tips
class SecurityTipsCardWidget extends StatelessWidget {
  /// Constructor
  const SecurityTipsCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نصائح الأمان',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const SecurityTipWidget(
              text: 'لا تشارك بيانات الدخول الخاصة بك مع أي شخص',
              icon: Icons.warning,
            ),
            const SecurityTipWidget(
              text: 'قم بتمكين المصادقة البيومترية للوصول السريع والآمن',
              icon: Icons.fingerprint,
            ),
            const SecurityTipWidget(
              text: 'احتفظ بتطبيقك محدثًا بأحدث إصدار',
              icon: Icons.update,
            ),
            const SecurityTipWidget(
              text: 'قم بتسجيل الخروج عند استخدام الأجهزة المشتركة',
              icon: Icons.logout,
            ),
          ],
        ),
      ),
    );
  }
}
