import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';

/// Controller responsible for managing account settings business logic
class AccountSettingsController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final RxBool isLoading = false.obs;
  final RxString selectedTwoFactorMethod = 'email'.obs;
  final RxString selectedBiometricMethod = 'fingerprint'.obs;

  // Reactive properties
  final _biometricEnabled = false.obs;
  final _twoFactorEnabled = false.obs;
  final _twoFactorMethod = 'email'.obs;
  final _biometricMethod = 'fingerprint'.obs;

  @override
  void onInit() {
    super.onInit();
    updateSecuritySettings();
  }

  /// Update security settings from user data
  void updateSecuritySettings() {
    final user = userController.currentUser;
    if (user != null) {
      _biometricEnabled.value = user.user.security.biometricAuth.enabled;
      _twoFactorEnabled.value = user.user.security.twoFactorAuth.enabled;

      // Set default methods if empty or not in the available options
      final twoFactorMethod = user.user.security.twoFactorAuth.method;
      final biometricMethod = user.user.security.biometricAuth.method;

      // Check if methods are valid, otherwise use defaults
      if (twoFactorMethod.isNotEmpty &&
          ['email', 'sms', 'authenticator', 'app'].contains(twoFactorMethod)) {
        _twoFactorMethod.value = twoFactorMethod;
      } else {
        _twoFactorMethod.value = 'app';
      }

      if (biometricMethod.isNotEmpty &&
          ['fingerprint', 'face', 'iris', 'app'].contains(biometricMethod)) {
        _biometricMethod.value = biometricMethod;
      } else {
        _biometricMethod.value = 'fingerprint';
      }
    }
  }

  /// Getters for user security settings
  RxBool get biometricEnabled => _biometricEnabled;
  RxBool get twoFactorEnabled => _twoFactorEnabled;
  RxString get twoFactorMethod => _twoFactorMethod;
  RxString get biometricMethod => _biometricMethod;

  /// Toggle biometric authentication
  Future<void> toggleBiometric(bool value) async {
    try {
      isLoading.value = true;
      final currentUser = userController.currentUser;
      if (currentUser == null) return;

      // Update local state immediately for better UX
      _biometricEnabled.value = value;

      final updatedUser = UserModel(
        user: User(
          id: currentUser.user.id,
          fullName: currentUser.user.fullName,
          email: currentUser.user.email,
          phone: currentUser.user.phone,
          gender: currentUser.user.gender,
          nationality: currentUser.user.nationality,
          dateOfBirth: currentUser.user.dateOfBirth,
          kycStatus: currentUser.user.kycStatus,
          verification: currentUser.user.verification,
          preferences: currentUser.user.preferences,
          notifications: currentUser.user.notifications,
          account: currentUser.user.account,
          address: currentUser.user.address,
          documents: currentUser.user.documents,
          contacts: currentUser.user.contacts,
          security: Security(
            twoFactorAuth: currentUser.user.security.twoFactorAuth,
            biometricAuth: BiometricAuth(
              enabled: value,
              method: _biometricMethod.value,
            ),
            devices: currentUser.user.security.devices,
          ),
        ),
      );

      await userController.updateUser(updatedUser);
      Get.snackbar(
        'نجاح',
        'تم ${value ? 'تفعيل' : 'تعطيل'} المصادقة البيومترية',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Revert local state on error
      updateSecuritySettings();
      Get.snackbar(
        'خطأ',
        'فشل تحديث إعدادات المصادقة البيومترية',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Toggle two-factor authentication
  Future<void> toggleTwoFactor(bool value) async {
    try {
      isLoading.value = true;
      final currentUser = userController.currentUser;
      if (currentUser == null) return;

      // Update local state immediately for better UX
      _twoFactorEnabled.value = value;

      final updatedUser = UserModel(
        user: User(
          id: currentUser.user.id,
          fullName: currentUser.user.fullName,
          email: currentUser.user.email,
          phone: currentUser.user.phone,
          gender: currentUser.user.gender,
          nationality: currentUser.user.nationality,
          dateOfBirth: currentUser.user.dateOfBirth,
          kycStatus: currentUser.user.kycStatus,
          verification: currentUser.user.verification,
          preferences: currentUser.user.preferences,
          notifications: currentUser.user.notifications,
          account: currentUser.user.account,
          address: currentUser.user.address,
          documents: currentUser.user.documents,
          contacts: currentUser.user.contacts,
          security: Security(
            twoFactorAuth: TwoFactorAuth(
              enabled: value,
              method: _twoFactorMethod.value,
              secretKey: currentUser.user.security.twoFactorAuth.secretKey,
            ),
            biometricAuth: currentUser.user.security.biometricAuth,
            devices: currentUser.user.security.devices,
          ),
        ),
      );

      await userController.updateUser(updatedUser);
      Get.snackbar(
        'نجاح',
        'تم ${value ? 'تفعيل' : 'تعطيل'} المصادقة الثنائية',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Revert local state on error
      updateSecuritySettings();
      Get.snackbar(
        'خطأ',
        'فشل تحديث إعدادات المصادقة الثنائية',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Set two-factor authentication method
  Future<void> setTwoFactorMethod(String method) async {
    try {
      isLoading.value = true;
      final currentUser = userController.currentUser;
      if (currentUser == null) return;

      // Update local state immediately for better UX
      _twoFactorMethod.value = method;

      final updatedUser = UserModel(
        user: User(
          id: currentUser.user.id,
          fullName: currentUser.user.fullName,
          email: currentUser.user.email,
          phone: currentUser.user.phone,
          gender: currentUser.user.gender,
          nationality: currentUser.user.nationality,
          dateOfBirth: currentUser.user.dateOfBirth,
          kycStatus: currentUser.user.kycStatus,
          verification: currentUser.user.verification,
          preferences: currentUser.user.preferences,
          notifications: currentUser.user.notifications,
          account: currentUser.user.account,
          address: currentUser.user.address,
          documents: currentUser.user.documents,
          contacts: currentUser.user.contacts,
          security: Security(
            twoFactorAuth: TwoFactorAuth(
              enabled: _twoFactorEnabled.value,
              method: method,
              secretKey: currentUser.user.security.twoFactorAuth.secretKey,
            ),
            biometricAuth: currentUser.user.security.biometricAuth,
            devices: currentUser.user.security.devices,
          ),
        ),
      );

      await userController.updateUser(updatedUser);
      Get.snackbar(
        'نجاح',
        'تم تحديث طريقة المصادقة الثنائية',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Revert local state on error
      updateSecuritySettings();
      Get.snackbar(
        'خطأ',
        'فشل تحديث طريقة المصادقة الثنائية',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Set biometric authentication method
  Future<void> setBiometricMethod(String method) async {
    try {
      isLoading.value = true;
      final currentUser = userController.currentUser;
      if (currentUser == null) return;

      // Update local state immediately for better UX
      _biometricMethod.value = method;

      final updatedUser = UserModel(
        user: User(
          id: currentUser.user.id,
          fullName: currentUser.user.fullName,
          email: currentUser.user.email,
          phone: currentUser.user.phone,
          gender: currentUser.user.gender,
          nationality: currentUser.user.nationality,
          dateOfBirth: currentUser.user.dateOfBirth,
          kycStatus: currentUser.user.kycStatus,
          verification: currentUser.user.verification,
          preferences: currentUser.user.preferences,
          notifications: currentUser.user.notifications,
          account: currentUser.user.account,
          address: currentUser.user.address,
          documents: currentUser.user.documents,
          contacts: currentUser.user.contacts,
          security: Security(
            twoFactorAuth: currentUser.user.security.twoFactorAuth,
            biometricAuth: BiometricAuth(
              enabled: _biometricEnabled.value,
              method: method,
            ),
            devices: currentUser.user.security.devices,
          ),
        ),
      );

      await userController.updateUser(updatedUser);
      Get.snackbar(
        'نجاح',
        'تم تحديث طريقة المصادقة البيومترية',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Revert local state on error
      updateSecuritySettings();
      Get.snackbar(
        'خطأ',
        'فشل تحديث طريقة المصادقة البيومترية',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
