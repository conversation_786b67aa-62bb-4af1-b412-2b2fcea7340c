import 'package:flutter/material.dart';

import '../../../utils/constants/colors.dart';
import '../../../utils/constants/sizes.dart';
import '../../../utils/helpers/helpers_functions.dart';

class CustomIconButton extends StatelessWidget {
  const CustomIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
  });

  final IconData icon;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return Container(
      decoration: BoxDecoration(
          color: dark ? AppColors.darkFillColor : AppColors.lightFillColor,
          borderRadius: BorderRadius.circular(100.0)
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon),
        padding: const EdgeInsets.all(AppSizes.md),
         color:  dark ? AppColors.darkIconsColor: AppColors.lightIconsColor
      )
    );
  }
}
