import 'package:flutter/material.dart';
import 'package:owallet/utils/helpers/helpers_functions.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// A unified card widget for consistent appearance across the app
class AppCardWidget extends StatelessWidget {
  /// The child widget to display inside the card
  final Widget child;

  /// Optional title for the card
  final String? title;

  /// Optional title style
  final TextStyle? titleStyle;

  /// Optional padding for the card content
  final EdgeInsetsGeometry? padding;

  /// Optional margin around the card
  final EdgeInsetsGeometry? margin;

  /// Optional background color
  final Color? backgroundColor;

  /// Optional elevation
  final double? elevation;

  /// Optional border radius
  final double? borderRadius;

  /// Optional leading widget (usually an icon)
  final Widget? leading;

  /// Optional trailing widget
  final Widget? trailing;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Constructor
  const AppCardWidget({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.leading,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    final cardContent = Padding(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Row(
              children: [
                if (leading != null) ...[leading!, const SizedBox(width: 8)],
                Expanded(
                  child: Text(
                    title!,
                    style:
                        titleStyle ??
                        const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                if (trailing != null) trailing!,
              ],
            ),
            const SizedBox(height: 16),
          ],
          child,
        ],
      ),
    );

    final card = Card(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      elevation: elevation ?? 2.0,
      color: dark ? AppColors.darkFillColor : AppColors.lightFillColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          borderRadius ?? AppSizes.borderRadiusLg,
        ),
      ),
      child: cardContent,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          borderRadius ?? AppSizes.borderRadiusLg,
        ),
        child: card,
      );
    }

    return card;
  }
}
