/*
import 'package:flutter/material.dart';
import '../../core/controllers/transaction_details_controller.dart';
import '../../utils/constants/sizes.dart';
import 'detail_row_widget.dart';

/// Widget for displaying the transaction information including details and notes
class TransactionInfoWidget extends StatelessWidget {
  /// The controller
  final TransactionDetailsController controller;

  /// Constructor
  const TransactionInfoWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailsCard(),
        SizedBox(height: AppSizes.md),
        if (controller.note.isNotEmpty) _buildNoteCard(),
      ],
    );
  }

  /// Build the details card
  Widget _buildDetailsCard() {
    return Padding(
      padding: EdgeInsets.all(AppSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DetailRowWidget(label: 'المستلم', value: controller.recipient ?? ""),
          const Divider(),
          DetailRowWidget(
            label: 'التاريخ',
            value: controller.getFormattedDate(),
          ),
          const Divider(),
          DetailRowWidget(label: 'رقم المرجع', value: controller.reference),
          if (controller.fee != null) ...[
            const Divider(),
            DetailRowWidget(
              label: 'الرسوم',
              value: controller.getFormattedFee() ?? '',
            ),
          ],
          if (controller.fromAccount != null) ...[
            const Divider(),
            DetailRowWidget(
              label: 'من حساب',
              value: controller.fromAccount ?? '',
            ),
          ],
        ],
      ),
    );
  }

  /// Build the note card
  Widget _buildNoteCard() {
    return Padding(
      padding: EdgeInsets.all(AppSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملاحظات',
            style: TextStyle(
              fontSize: AppSizes.titleMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppSizes.sm),
          Text(controller.note),
        ],
      ),
    );
  }
}

*/
