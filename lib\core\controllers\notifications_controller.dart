import 'package:get/get.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';

/// Controller responsible for managing notifications in the UI
class NotificationsController extends GetxController {
  // Notification service
  late final NotificationService _notificationService;

  // Reactive variables
  final RxBool _isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _isLoading.value = true;

    // Try to find NotificationService
    try {
      _notificationService = Get.find<NotificationService>();
      _isLoading.value = false;
    } catch (e) {
      // If NotificationService is not found, create it
      Get.log('NotificationService not found, creating it now', isError: true);
      _notificationService = Get.put(NotificationService(), permanent: true);

      // Short delay to ensure notification service is initialized
      Future.delayed(const Duration(milliseconds: 300), () {
        _isLoading.value = false;
      });
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String id) async {
    await _notificationService.markAsRead(id);
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _notificationService.markAllAsRead();
  }

  // Getters
  List<NotificationModel> get notifications =>
      _notificationService.notifications;
  bool get isLoading => _isLoading.value;
  int get unreadCount => _notificationService.unreadCount;
}
