import 'package:flutter/material.dart';
import '../../utils/constants/sizes.dart';
import '../../common/widgets/avatar_widget.dart';

/// Widget for displaying a quick action item
class QuickActionItemWidget extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The callback when the item is tapped
  final VoidCallback onPressed;

  /// Constructor
  const QuickActionItemWidget({
    super.key,
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon: AvatarWidget(icon: icon, size: 55, iconSize: 24),
      padding: const EdgeInsets.all(AppSizes.md),
    );
  }
}
