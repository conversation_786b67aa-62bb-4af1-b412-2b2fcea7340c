import 'package:flutter/material.dart';
import '../../constants/colors.dart';

class CustomChipTheme {
  CustomChipTheme._();

  static ChipThemeData light = ChipThemeData(
    disabledColor: AppColors.lightTitleColor.withOpacity(0.4),
    labelStyle: const TextStyle(color: AppColors.lightTitleColor),
    selectedColor: AppColors.primary,
    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
    checkmarkColor: AppColors.lightFillColor,
  );

  static ChipThemeData dark = ChipThemeData(
    disabledColor: AppColors.darkFillColor.withOpacity(0.1),
    labelStyle: const TextStyle(color: AppColors.darkFillColor),
    selectedColor: AppColors.primary,
    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
    checkmarkColor: AppColors.darkFillColor,
  );
}
