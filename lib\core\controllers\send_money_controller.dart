import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';

class SendMoneyController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final isLoading = false.obs;
  final selectedContact = Rxn<Contact>();
  final canSend = false.obs;
  final searchQuery = ''.obs;
  final RxList<Contact> contacts = <Contact>[].obs;
  final RxString selectedCurrency = 'USD'.obs;
  final RxDouble exchangeRate = 1.0.obs;
  final RxDouble fee = 0.0.obs;
  final RxDouble totalAmount = 0.0.obs;

  final amountController = TextEditingController();
  final descriptionController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    ever(selectedContact, (_) => validateForm());
    ever(RxString(amountController.text), (_) => validateForm());
    ever(selectedCurrency, (_) => calculateTotal());
    loadContacts();
    ever(searchQuery, (_) => filterContacts());
  }

  @override
  void onClose() {
    amountController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  Future<void> loadContacts() async {
    final user = userController.currentUser;
    if (user == null) return;

    contacts.clear();
    contacts.addAll(user.user.contacts.toList());
    filterContacts();
  }

  List<Contact> get filteredContacts {
    if (searchQuery.value.isEmpty) {
      return contacts;
    }
    final query = searchQuery.value.toLowerCase();
    return contacts
        .where(
          (contact) =>
              contact.name.toLowerCase().contains(query) ||
              contact.account.toLowerCase().contains(query),
        )
        .toList();
  }

  void filterContacts() {
    contacts.refresh();
  }

  void selectContact(Contact contact) {
    selectedContact.value = contact;
    validateForm();
  }

  void updateAmount(String value) {
    validateForm();
    calculateTotal();
  }

  void updateDescription(String value) {
    validateForm();
  }

  void setCurrency(String currency) {
    selectedCurrency.value = currency;
    calculateTotal();
  }

  void calculateTotal() {
    final amount = double.tryParse(amountController.text) ?? 0.0;
    final rate = exchangeRate.value;
    final calculatedFee = calculateFee(amount);

    fee.value = calculatedFee;
    totalAmount.value = (amount * rate) + calculatedFee;
  }

  double calculateFee(double amount) {
    // Example fee calculation: 1% of amount with minimum of $1
    return (amount * 0.01).clamp(1.0, 10.0);
  }

  void validateForm() {
    if (selectedContact.value == null) {
      canSend.value = false;
      return;
    }

    final amount = double.tryParse(amountController.text);
    if (amount == null || amount <= 0) {
      canSend.value = false;
      return;
    }

    final user = userController.currentUser;
    if (user == null || totalAmount.value > (user.user.account?.balance ?? 0)) {
      canSend.value = false;
      return;
    }

    canSend.value = true;
  }

  Future<void> sendMoney() async {
    if (!canSend.value) return;

    try {
      isLoading.value = true;

      final contact = selectedContact.value;
      if (contact == null) {
        Get.snackbar(
          'خطأ',
          'الرجاء اختيار جهة اتصال',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      final amount = double.parse(amountController.text);
      final description =
          descriptionController.text.isEmpty
              ? 'تحويل إلى ${contact.name}'
              : descriptionController.text;

      final result = await userController.transferMoney(
        contact.account,
        amount,
        description,
      );

      if (result['success'] == true) {
        Get.snackbar(
          'نجاح',
          'تم إرسال المبلغ بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Clear form
        selectedContact.value = null;
        amountController.clear();
        descriptionController.clear();
        selectedCurrency.value = 'USD';
        validateForm();

        // Navigate back to home
        Get.offAllNamed('/home');
      } else {
        Get.snackbar(
          'خطأ',
          result['message'] ?? 'فشل في إرسال المبلغ',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إرسال المبلغ: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
