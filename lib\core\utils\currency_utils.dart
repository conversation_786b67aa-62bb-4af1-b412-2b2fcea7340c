import '../enums/app_enums.dart';

/// Utility class for currency-related operations
class CurrencyUtils {
  /// Convert Currency enum to its string representation
  static String currencyToString(Currency currency) {
    return currency.toString().split('.').last.toUpperCase();
  }

  /// Convert string to Currency enum
  static Currency stringToCurrency(String currencyStr) {
    try {
      return Currency.values.firstWhere(
        (c) => c.toString().split('.').last.toUpperCase() == currencyStr.toUpperCase(),
        orElse: () => Currency.usd,
      );
    } catch (e) {
      return Currency.usd;
    }
  }

  /// Get currency symbol for a given Currency enum
  static String getCurrencySymbol(Currency currency) {
    switch (currency) {
      case Currency.usd:
        return '\$';
      case Currency.eur:
        return '€';
      case Currency.gbp:
        return '£';
      case Currency.sar:
        return 'SAR';
      case Currency.aed:
        return 'AED';
    }
  }

  /// Format amount with currency symbol
  static String formatAmount(double amount, Currency currency) {
    final symbol = getCurrencySymbol(currency);
    return '$symbol ${amount.toStringAsFixed(2)}';
  }

  /// Get full currency name
  static String getCurrencyName(Currency currency) {
    switch (currency) {
      case Currency.usd:
        return 'US Dollar';
      case Currency.eur:
        return 'Euro';
      case Currency.gbp:
        return 'British Pound';
      case Currency.sar:
        return 'Saudi Riyal';
      case Currency.aed:
        return 'UAE Dirham';
    }
  }

  /// Get list of all available currencies as strings
  static List<String> getAllCurrencies() {
    return Currency.values.map((c) => currencyToString(c)).toList();
  }
}
