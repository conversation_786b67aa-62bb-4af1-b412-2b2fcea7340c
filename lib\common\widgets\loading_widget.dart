import 'package:flutter/material.dart';

/// A shared widget for displaying a loading state
class LoadingWidget extends StatelessWidget {
  /// The size of the loading indicator
  final double size;
  
  /// The color of the loading indicator
  final Color? color;
  
  /// The stroke width of the loading indicator
  final double strokeWidth;

  /// Constructor
  const LoadingWidget({
    super.key,
    this.size = 40,
    this.color,
    this.strokeWidth = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          color: color,
          strokeWidth: strokeWidth,
        ),
      ),
    );
  }
}
