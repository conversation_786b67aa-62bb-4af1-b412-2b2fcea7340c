import 'package:flutter/material.dart';
import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../../common/widgets/custom_button/custom_button.dart';
import '../../../utils/constants/colors.dart';
import '../../common/widgets/text form field/costume_text_field.dart';

class ModifyPinCode extends StatefulWidget {
  const ModifyPinCode({super.key});

  @override
  ModifyPinCodeState createState() => ModifyPinCodeState();
}

class ModifyPinCodeState extends State<ModifyPinCode> {
  final TextEditingController _currentPinController = TextEditingController();
  final TextEditingController _newPinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();

  void _confirmPinChange() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('PIN code has change')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BuildAppBar(
        showBackArrow: true,
        title: Text('Modify your login PIN code'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: <Widget>[
            Text(
              '*After changing the PIN code, you will be prevented from financial transactions for 24 hours.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.apply(color: AppColors.error),
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              controller: _currentPinController,
              obscureText: true,
              hintText: 'Please enter the current PIN cod',
              filled: true,
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              controller: _newPinController,
              obscureText: true,
              hintText: 'Please enter a new PIN coed',
              filled: true,
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              controller: _confirmPinController,
              obscureText: true,
              hintText: 'Enter again',
              filled: true,
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                onPressed: _confirmPinChange,
                label: 'Confirm',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
