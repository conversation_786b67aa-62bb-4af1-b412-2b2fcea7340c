import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../common/widgets/shared_widgets.dart';
import '../widgets/help/app_info_card_widget.dart';
import '../widgets/help/content_section_widget.dart';
import '../widgets/help/faq_card_widget.dart';
import '../widgets/help/support_card_widget.dart';

// Simple page to display user guide content
class UserGuidePage extends StatelessWidget {
  const UserGuidePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Guide'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ContentSectionWidget(
              title: 'Getting Started',
              content:
                  'Welcome to OWallet! This guide will help you navigate through the app and make the most of its features.',
              titleStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),

            // Add more sections as needed
            const ContentSectionWidget(
              title: 'Managing Your Account',
              content:
                  'Learn how to update your profile, change security settings, and manage your account preferences.',
            ),

            // More content would go here
          ],
        ),
      ),
    );
  }
}

// Page to display privacy policy
class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ContentSectionWidget(
              title: 'Privacy Policy',
              content:
                  'This Privacy Policy describes how OWallet collects, uses, and discloses your personal information when you use our mobile application.',
              titleStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              'Last updated: March 15, 2024',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
            SizedBox(height: 24),
            // More content would go here
          ],
        ),
      ),
    );
  }
}

// Page to display terms of service
class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ContentSectionWidget(
              title: 'Terms of Service',
              content:
                  'Please read these Terms of Service carefully before using the OWallet mobile application.',
              titleStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              'Last updated: March 15, 2024',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
            SizedBox(height: 24),
            // More content would go here
          ],
        ),
      ),
    );
  }
}

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Help & Support'),
      ),

      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact Support
            const SectionHeaderWidget(
              title: 'Contact Support',
              showDivider: true,
            ),
            SupportCardWidget(
              title: 'Email Support',
              subtitle: 'Get help via email',
              icon: Icons.email,
              onTap: () {
                // Launch email client with support email
                final Uri emailLaunchUri = Uri(
                  scheme: 'mailto',
                  path: '<EMAIL>',
                  queryParameters: {
                    'subject': 'Support Request',
                    'body': 'Hello, I need help with...',
                  },
                );
                _launchUrl(emailLaunchUri.toString());
              },
            ),
            SupportCardWidget(
              title: 'Live Chat',
              subtitle: 'Chat with our support team',
              icon: Icons.chat,
              onTap: () {
                // Open live chat in a web view
                _launchUrl('https://owallet.com/chat');
              },
            ),
            SupportCardWidget(
              title: 'Call Support',
              subtitle: 'Speak with our support team',
              icon: Icons.phone,
              onTap: () {
                // Launch phone dialer with support number
                _launchUrl('tel:******-OWALLET');
              },
            ),

            const SizedBox(height: 32),

            // FAQs
            const SectionHeaderWidget(
              title: 'Frequently Asked Questions',
              showDivider: true,
            ),
            FAQCardWidget(
              question: 'How do I reset my password?',
              answer:
                  'You can reset your password by clicking on the "Forgot Password" link on the login screen.',
            ),
            FAQCardWidget(
              question: 'How do I update my profile?',
              answer:
                  'Go to the Profile tab and tap on the edit icon to update your information.',
            ),
            FAQCardWidget(
              question: 'How do I enable two-factor authentication?',
              answer:
                  'Go to Settings > Security > Two-Factor Authentication and follow the setup process.',
            ),
            FAQCardWidget(
              question: 'How do I change my notification settings?',
              answer:
                  'Navigate to Settings > Notifications to customize your notification preferences.',
            ),

            const SizedBox(height: 32),

            // Resources
            const SectionHeaderWidget(title: 'Resources', showDivider: true),
            SupportCardWidget(
              title: 'User Guide',
              subtitle: 'Learn how to use the app',
              icon: Icons.book,
              onTap: () {
                // Navigate to user guide page
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const UserGuidePage(),
                  ),
                );
              },
            ),
            SupportCardWidget(
              title: 'Privacy Policy',
              subtitle: 'Read our privacy policy',
              icon: Icons.privacy_tip,
              onTap: () {
                // Navigate to privacy policy page
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyPage(),
                  ),
                );
              },
            ),
            SupportCardWidget(
              title: 'Terms of Service',
              subtitle: 'Read our terms of service',
              icon: Icons.description,
              onTap: () {
                // Navigate to terms of service page
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const TermsOfServicePage(),
                  ),
                );
              },
            ),

            const SizedBox(height: 32),

            // App Info
            const AppInfoCardWidget(
              version: '1.0.0',
              buildNumber: '100',
              lastUpdated: 'March 15, 2024',
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
