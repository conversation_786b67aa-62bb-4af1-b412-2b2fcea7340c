import 'package:flutter/material.dart';

/// Widget for displaying an action button
class ActionButtonWidget extends StatelessWidget {
  /// The label to display
  final String label;
  
  /// The callback when the button is tapped
  final VoidCallback onTap;
  
  /// The color of the button
  final Color color;
  
  /// The icon to display
  final IconData icon;

  /// Constructor
  const ActionButtonWidget({
    super.key,
    required this.label,
    required this.onTap,
    required this.color,
    this.icon = Icons.arrow_forward,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onTap,
      icon: Icon(icon, color: Colors.white),
      label: Text(label, style: const TextStyle(color: Colors.white)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
