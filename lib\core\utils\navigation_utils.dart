import 'package:get/get.dart';
import '../models/user_model.dart';
import '../../routes/app_routes.dart';

/// Utility class for navigation-related operations
class NavigationUtils {
  /// Navigate to transaction details screen
  static void navigateToTransactionDetails(Transaction transaction) {
    Get.toNamed(
      AppRoutes.transactionDetails,
      arguments: {
        'recipient': transaction.toAccount,
        'amount': transaction.amount,
        'transactionType': transaction.type,
        'transactionDate': transaction.timestamp,
        'note': transaction.description,
        'status': transaction.status,
        'reference': transaction.reference,
        'fee': transaction.fee,
        'currency': transaction.currency,
        'fromAccount': transaction.fromAccount,
      },
    );
  }

  /// Navigate to send money screen
  static void navigateToSendMoney() {
    Get.toNamed(AppRoutes.sendMoney);
  }

  /// Navigate to receive money screen
  static void navigateToReceiveMoney() {
    Get.toNamed(AppRoutes.receiveMoney);
  }

  /// Navigate to transfer screen
  static void navigateToTransfer() {
    Get.toNamed(AppRoutes.transfer);
  }

  /// Navigate to payment screen
  static void navigateToPayment({String amount = '0'}) {
    Get.toNamed(AppRoutes.payment, arguments: {'amount': amount});
  }

  /// Navigate to contacts screen
  static void navigateToContacts() {
    Get.toNamed(AppRoutes.contacts);
  }

  /// Navigate to withdraw funds screen
  static void navigateToWithdrawFunds() {
    Get.toNamed(AppRoutes.withdrawFunds);
  }

  /// Navigate to transaction history screen
  static void navigateToTransactionHistory() {
    Get.toNamed(AppRoutes.transactionHistory);
  }

  /// Navigate to top up wallet (deposit) screen
  static void navigateToTopUpWallet() {
    Get.toNamed(AppRoutes.topUpWallet);
  }

  /// Navigate to bills screen
  static void navigateToBills() {
    Get.toNamed(AppRoutes.bills);
  }

  /// Navigate to account screen
  static void navigateToAccount() {
    Get.toNamed(AppRoutes.account);
  }

  /// Navigate to credit cards screen
  static void navigateToCreditCards() {
    Get.toNamed('/credit-cards');
  }

  /// Navigate to settings screen
  static void navigateToSettings() {
    Get.toNamed(AppRoutes.settings);
  }

  /// Navigate to help and support screen
  static void navigateToHelpSupport() {
    Get.toNamed(AppRoutes.helpSupport);
  }

  /// Navigate to notifications screen
  static void navigateToNotifications() {
    Get.toNamed(AppRoutes.notifications);
  }

  /// Navigate back
  static void goBack() {
    Get.back();
  }

  /// Show error snackbar
  static void showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error.withAlpha(180),
      colorText: Get.theme.colorScheme.onError,
    );
  }

  /// Show success snackbar
  static void showSuccessSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.primary.withAlpha(180),
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }

  /// Show info snackbar
  static void showInfoSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.secondary.withAlpha(180),
      colorText: Get.theme.colorScheme.onSecondary,
    );
  }
}
