import 'package:flutter/material.dart';
import '../core/models/user_model.dart';
import 'account/device_tile_widget.dart';
import 'account/security_tips_card_widget.dart';
import 'account/status_tile_widget.dart';
import 'settings/dropdown_tile_widget.dart';
import 'settings/switch_tile_widget.dart';

/// Re-export widgets for backward compatibility
export 'account/device_tile_widget.dart';
export 'account/security_tips_card_widget.dart';
export 'account/status_tile_widget.dart';

/// Build a switch tile with icon and subtitle
SwitchTileWidget buildSwitchTile(
  String title,
  IconData icon,
  String subtitle,
  bool value,
  Future<void> Function(bool) onChanged,
) {
  return SwitchTileWidget(
    title: title,
    leadingIcon: icon,
    subtitle: subtitle,
    value: value,
    onChanged: onChanged,
  );
}

/// Build a dropdown tile with icon
DropdownTileWidget buildDropdownTile(
  String title,
  IconData icon,
  String value,
  List<String> items,
  ValueChanged<String?> onChanged,
) {
  return DropdownTileWidget(
    title: title,
    leadingIcon: icon,
    value: value,
    items: items,
    onChanged: onChanged,
  );
}

/// Build a device tile with expandable details
DeviceTileWidget buildDeviceTile(Device device) {
  return DeviceTileWidget(device: device);
}

/// Build a security tips card
SecurityTipsCardWidget buildSecurityTipsCard() {
  return const SecurityTipsCardWidget();
}

/// Build a status tile with title, status, and tap action
StatusTileWidget buildStatusTile(
  String title,
  VoidCallback onTap, {
  String? status,
  IconData? leadingIcon,
}) {
  return StatusTileWidget(
    title: title,
    status: status,
    onTap: onTap,
    leadingIcon: leadingIcon,
    trailingIcon: Icons.arrow_forward_ios,
  );
}
