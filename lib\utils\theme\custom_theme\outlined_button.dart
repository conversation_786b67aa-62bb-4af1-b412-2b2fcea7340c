import 'package:flutter/material.dart';

import '../../constants/colors.dart';
import '../../constants/sizes.dart';

class CustomOutlinedButtonTheme {
  /// --To Avoid Creating Instances
  CustomOutlinedButtonTheme._();

  /// -- Light Theme
  static final light = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      elevation: 0.0,
      shadowColor: Colors.transparent,
      backgroundColor: AppColors.light,
      foregroundColor: AppColors.lightTitleColor,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      side: const BorderSide(color: AppColors.lightBorderColor, width: 1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      textStyle: const TextStyle(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor),
    ),
  );

  /// -- Light Theme
  static final dark = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      elevation: 0.0,
      shadowColor: Colors.transparent,
      backgroundColor: AppColors.dark,
      foregroundColor: AppColors.darkTitleColor,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      side: const BorderSide(color: AppColors.darkBorderColor, width: 1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      textStyle: const TextStyle(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor),
    ),
  );
}
