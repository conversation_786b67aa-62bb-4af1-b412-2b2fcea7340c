import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import '../../../utils/constants/sizes.dart';

/// A reusable search bar widget that can be used across the app
class SearchBarWidget extends StatelessWidget {
  /// The hint text to display in the search bar
  final String hintText;

  /// The callback function when the search query changes
  final Function(String) onChanged;

  /// The height of the search bar
  final double height;

  /// The padding around the search bar
  final EdgeInsetsGeometry padding;

  /// The icon to display in the search bar
  final IconData prefixIcon;

  /// The size of the icon
  final double iconSize;

  /// The border radius of the search bar
  final double borderRadius;

  /// Constructor
  const SearchBarWidget({
    super.key,
    required this.onChanged,
    this.hintText = 'Search...',
    this.height = 45.0,
    this.padding = const EdgeInsets.all(AppSizes.md),
    this.prefixIcon = Iconsax.search_normal_1,
    this.iconSize = AppSizes.iconMd - 2.0, // 22
    this.borderRadius = 100.0,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: SizedBox(
        height: height,
        child: TextField(
          decoration: InputDecoration(
            filled: true,
            hintText: hintText,
            prefixIcon: Icon(prefixIcon, size: iconSize),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                width: 0.0,
                color: Colors.transparent,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                width: 0.0,
                color: Colors.transparent,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                width: 0.0,
                color: Colors.transparent,
              ),
            ),
          ),
          onChanged: onChanged,
        ),
      ),
    );
  }
}
