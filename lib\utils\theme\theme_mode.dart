import 'package:flutter/material.dart' as flutter;
import 'package:get/get.dart';
import '../../core/enums/app_enums.dart';

/// Converts app_enums.ThemeMode to Flutter's ThemeMode
///
/// This function takes our app's ThemeMode enum and converts it to
/// the corresponding Flutter ThemeMode for use with MaterialApp.
flutter.ThemeMode getThemeMode(dynamic appThemeMode) {
  // Handle the case when appThemeMode is null or not a valid ThemeMode
  if (appThemeMode == null) {
    return flutter.ThemeMode.system;
  }

  // If it's already a Flutter ThemeMode, return it directly
  if (appThemeMode is flutter.ThemeMode) {
    return appThemeMode;
  }

  // Convert string representation to ThemeMode if needed
  if (appThemeMode is String) {
    if (appThemeMode.contains('dark')) {
      return flutter.ThemeMode.dark;
    } else if (appThemeMode.contains('system')) {
      return flutter.ThemeMode.system;
    } else {
      return flutter.ThemeMode.light;
    }
  }

  // Handle our custom ThemeMode enum
  try {
    if (appThemeMode is ThemeMode) {
      switch (appThemeMode) {
        case ThemeMode.dark:
          return flutter.ThemeMode.dark;
        case ThemeMode.system:
          return flutter.ThemeMode.system;
        case ThemeMode.light:
          return flutter.ThemeMode.light;
      }
    }

    // Try to convert from string representation
    final String modeString = appThemeMode.toString().toLowerCase();
    if (modeString.contains('dark')) {
      return flutter.ThemeMode.dark;
    } else if (modeString.contains('system')) {
      return flutter.ThemeMode.system;
    }
  } catch (e) {
    // If any error occurs, default to light mode
    Get.log('Error converting theme mode: $e');
  }

  // Default to light mode
  return flutter.ThemeMode.light;
}
