enum UserRole { admin, user, merchant }

enum TransactionStatus { pending, completed, failed, cancelled }

enum TransactionType { all, send, receive, topup, withdraw }

enum PaymentMethod { bankTransfer, creditCard, mobileMoney, cash }

enum NotificationType { transaction, system, promotion, security }

enum Language { arabic, english, french }

enum ThemeMode { light, dark, system }

enum Currency { usd, eur, gbp, sar, aed }

enum KycStatus { verified, pending, rejected, notStarted }

enum AccountType {checking, saving}
