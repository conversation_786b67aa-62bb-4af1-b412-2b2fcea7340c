class AppSizes {
  AppSizes._();

  // Padding and margin sizes
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;

  // Icon sizes
  static const double iconXs = 12.0;
  static const double iconSm = 16.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;

  // Font sizes
  static const double fontSizeSm = 14.0;
  static const double fontSizeMd = 16.0;
  static const double fontSizeLg = 18.0;

  static const double headlineSmall = 18;
  static const double headlineMedium = 24;
  static const double headlineLarge = 32;

  static const double titleSmall = 16;
  static const double titleMedium = 18;
  static const double titleLarge = 24;

  static const double bodySmall = 12;
  static const double bodyMedium = 14;
  static const double bodyLarge = 16;

  static const double labelSmall = 10;
  static const double labelMedium = 12;
  static const double labelLarge = 14;

  // Button sizes
  static const double buttonElevation = 4.0;
  static const double buttonRadius = 12.0;
  static const double buttonHeight = 18.0;
  static const double buttonWidth = 120.0;

  // AppBar height
  static const double appBarHeight = 56.0;

  // Image sizes
  static const double imageThumbSize = 80.0;

  // Default spacing between sections
  static const double spaceBtwItems = 16.0;
  static const double defaultSpace = 24.0;
  static const double spaceBtwSections = 32.0;

  // Border radius
  static const double borderRadiusSm = 4.0;
  static const double borderRadiusMd = 8.0;
  static const double borderRadiusLg = 12.0;    
  static const double borderRadiusXl = 16.0;
  static const double borderRadiusXxl = 24.0;



  // Divider height
  static const double dividerHeight = 1.0;

  // Product item dimensions
  static const double productImageRadius = 16.0;
  static const double productImageSize = 120.0;
  static const double productItemHeight = 160.0;

  // Input field
  static const double inputFieldRadius = 12.0;
  static const double spaceBtwInputFields = 16.0;

  // Card sizes
  static const double cardElevation = 2.0;
  static const double cardRadiusXs = 6.0;
  static const double cardRadiusMd = 12.0;
  static const double cardRadiusLg = 16.0;
  static const double cardRadiusSm = 18.0;

  // Image carousel height
  static const double imageCarouselHeight = 200.0;

  // Loading indicator size
  static const double loadingIndicatorSize = 36.0;

  // Grid view spacing
  static const double gridViewSpacing = 16.0;

  // Responsive Screen Sizes
  static const int mobileScreenSize = 360;
  static const int tabletScreenSize = 768;
  static const int customScreenSize = 1100;
  static const int desktopScreenSize = 1366;
}
