import 'package:flutter/material.dart';

/// Widget for displaying a content section with title and text
class ContentSectionWidget extends StatelessWidget {
  /// The title of the section
  final String title;
  
  /// The content text
  final String content;
  
  /// Optional title style
  final TextStyle? titleStyle;
  
  /// Optional content style
  final TextStyle? contentStyle;
  
  /// Optional spacing between title and content
  final double spacing;

  /// Constructor
  const ContentSectionWidget({
    super.key,
    required this.title,
    required this.content,
    this.titleStyle,
    this.contentStyle,
    this.spacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: titleStyle ?? 
            const TextStyle(
              fontSize: 18, 
              fontWeight: FontWeight.bold,
            ),
        ),
        SizedBox(height: spacing),
        Text(
          content,
          style: contentStyle ?? 
            const TextStyle(
              fontSize: 16,
            ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
