import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../enums/app_enums.dart' as app_enums;
import '../utils/currency_utils.dart';
import '../constants/app_constants.dart';

/// Controller responsible for managing application settings
class SettingsController extends GetxController {
  // Use ThemeMode enum for dark mode
  final Rx<app_enums.ThemeMode> _themeMode = app_enums.ThemeMode.light.obs;

  // Use Language enum for language
  final Rx<app_enums.Language> _selectedLanguage =
      app_enums.Language.english.obs;

  // Notifications settings
  final _notificationsEnabled = true.obs;

  // Use Currency enum for currency
  final Rx<app_enums.Currency> _selectedCurrency = app_enums.Currency.usd.obs;

  // Convert enums to display strings
  final Map<app_enums.Language, String> _languageNames = {
    app_enums.Language.english: 'English',
    app_enums.Language.arabic: 'Arabic',
    app_enums.Language.french: 'French',
  };

  @override
  void onInit() {
    super.onInit();
    // Load saved settings from storage
    _loadSettings();
  }

  /// Load settings from storage
  void _loadSettings() {
    try {
      final box = GetStorage();
      final settings = box.read(AppConstants.settingsKey);

      if (settings != null) {
        // Load theme mode
        final themeMode = settings['themeMode'];
        if (themeMode != null) {
          _themeMode.value = app_enums.ThemeMode.values.firstWhere(
            (e) => e.toString() == themeMode,
            orElse: () => app_enums.ThemeMode.light,
          );
        }

        // Load language
        final language = settings['language'];
        if (language != null) {
          _selectedLanguage.value = app_enums.Language.values.firstWhere(
            (e) => e.toString() == language,
            orElse: () => app_enums.Language.english,
          );
        }

        // Load notifications setting
        final notifications = settings['notifications'];
        if (notifications != null) {
          _notificationsEnabled.value = notifications;
        }

        // Load currency
        final currency = settings['currency'];
        if (currency != null) {
          _selectedCurrency.value = app_enums.Currency.values.firstWhere(
            (e) => e.toString() == currency,
            orElse: () => app_enums.Currency.usd,
          );
        }
      }
    } catch (e) {
      Get.log('Error loading settings: $e', isError: true);
      // If there's an error, use default settings
    }
  }

  /// Save settings to storage
  void _saveSettings() {
    try {
      final box = GetStorage();
      final settings = {
        'themeMode': _themeMode.value.toString(),
        'language': _selectedLanguage.value.toString(),
        'notifications': _notificationsEnabled.value,
        'currency': _selectedCurrency.value.toString(),
      };

      box.write(AppConstants.settingsKey, settings);
    } catch (e) {
      Get.log('Error saving settings: $e', isError: true);
    }
  }

  /// Toggle dark mode (for backward compatibility)
  void toggleDarkMode(bool value) {
    _themeMode.value =
        value ? app_enums.ThemeMode.dark : app_enums.ThemeMode.light;
    _saveSettings();
  }

  /// Set theme mode
  void setThemeMode(String? value) {
    if (value != null) {
      switch (value) {
        case 'Light':
          _themeMode.value = app_enums.ThemeMode.light;
          break;
        case 'Dark':
          _themeMode.value = app_enums.ThemeMode.dark;
          break;
        case 'System':
          _themeMode.value = app_enums.ThemeMode.system;
          break;
      }
      _saveSettings();
    }
  }

  /// Set application language
  void setLanguage(String? value) {
    if (value != null) {
      // Find the Language enum that matches the display name
      final language =
          _languageNames.entries
              .firstWhere(
                (entry) => entry.value == value,
                orElse: () => MapEntry(app_enums.Language.english, 'English'),
              )
              .key;
      _selectedLanguage.value = language;
      _saveSettings();
    }
  }

  /// Toggle notifications
  void toggleNotifications(bool value) {
    _notificationsEnabled.value = value;
    _saveSettings();
  }

  /// Set default currency
  void setCurrency(String? value) {
    if (value != null) {
      _selectedCurrency.value = CurrencyUtils.stringToCurrency(value);
      _saveSettings();
    }
  }

  // Getters
  bool get isDarkMode => _themeMode.value == app_enums.ThemeMode.dark;

  // Get the current theme mode
  app_enums.ThemeMode get themeMode => _themeMode.value;

  // Get theme mode as a string for display
  String get themeModeString {
    switch (_themeMode.value) {
      case app_enums.ThemeMode.light:
        return 'Light';
      case app_enums.ThemeMode.dark:
        return 'Dark';
      case app_enums.ThemeMode.system:
        return 'System';
    }
  }

  // Get all available theme modes as strings
  List<String> get themeModes => ['Light', 'Dark', 'System'];

  String get selectedLanguage =>
      _languageNames[_selectedLanguage.value] ?? 'English';

  RxBool get notificationsEnabled => _notificationsEnabled;

  // Currency getters
  String get selectedCurrencyString =>
      CurrencyUtils.currencyToString(_selectedCurrency.value);

  app_enums.Currency get selectedCurrencyEnum => _selectedCurrency.value;

  List<String> get languages => _languageNames.values.toList();

  List<String> get currencies => CurrencyUtils.getAllCurrencies();
}
