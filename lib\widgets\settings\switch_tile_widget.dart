import 'package:flutter/material.dart';

import '../../utils/constants/colors.dart';
import '../../utils/helpers/helpers_functions.dart';

/// Widget for displaying a switch tile with icon
class SwitchTileWidget extends StatelessWidget {
  /// The title of the tile
  final String title;

  /// The icon to display
  final IconData? leadingIcon;

  /// The current value of the switch
  final bool value;

  /// Callback when the switch value changes
  final ValueChanged<bool> onChanged;

  /// Optional subtitle text
  final String? subtitle;

  /// Constructor
  const SwitchTileWidget({
    super.key,
    required this.title,
    required this.leadingIcon,
    required this.value,
    required this.onChanged,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);

    return ListTile(
      leading:
          leadingIcon != null
              ? Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color:
                      dark ? AppColors.darkFillColor : AppColors.lightFillColor,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(leadingIcon),
              )
              : null,
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
