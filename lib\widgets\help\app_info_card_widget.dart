import 'package:flutter/material.dart';
import 'info_row_widget.dart';

/// Widget for displaying app information card
class AppInfoCardWidget extends StatelessWidget {
  /// The app version
  final String version;
  
  /// The build number
  final String buildNumber;
  
  /// The last updated date
  final String lastUpdated;

  /// Constructor
  const AppInfoCardWidget({
    super.key,
    required this.version,
    required this.buildNumber,
    required this.lastUpdated,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'App Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            InfoRowWidget(label: 'Version', value: version),
            InfoRowWidget(label: 'Build Number', value: buildNumber),
            InfoRowWidget(label: 'Last Updated', value: lastUpdated),
          ],
        ),
      ),
    );
  }
}
