import 'package:flutter/material.dart';

/// Widget for displaying a filter button
class FilterButtonWidget extends StatelessWidget {
  /// The button text
  final String text;

  /// The icon to display
  final IconData icon;

  /// Callback when button is pressed
  final VoidCallback onPressed;

  /// Whether the button is primary or secondary
  final bool isPrimary;

  /// Constructor
  const FilterButtonWidget({
    super.key,
    required this.text,
    required this.icon,
    required this.onPressed,
    this.isPrimary = true,
  });

  @override
  Widget build(BuildContext context) {
    return isPrimary
        ? ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon),
          label: Text(text),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        )
        : TextButton.icon(
          onPressed: onPressed,
          icon: Icon(icon),
          label: Text(text),
        );
  }
}
