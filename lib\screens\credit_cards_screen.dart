import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/credit_cards_controller.dart';
import '../core/models/user_model.dart';

/// Screen for displaying and managing credit cards
class CreditCardsScreen extends StatelessWidget {
  const CreditCardsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreditCardsController>();

    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Credit Cards'),
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : _buildBody(controller, context),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.addCreditCard,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody(CreditCardsController controller, BuildContext context) {
    if (controller.creditCards.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.creditCards.length,
      itemBuilder: (context, index) {
        final card = controller.creditCards[index];
        return _buildCreditCardItem(card, controller, context);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.credit_card_off, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          const Text(
            'لا توجد بطاقات ائتمان',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'اضغط على زر الإضافة لإضافة بطاقة جديدة',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditCardItem(
    CreditCard card,
    CreditCardsController controller,
    BuildContext context,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.blue.shade800, Colors.blue.shade500],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  card.type,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Icon(
                  controller.getCardTypeIcon(card.type),
                  color: Colors.white,
                  size: 32,
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              controller.formatCardNumber(card.maskedNumber),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                letterSpacing: 2,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تاريخ الانتهاء',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                    Text(
                      card.expiresAt,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الحالة',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                    Row(
                      children: [
                        Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: controller.getCardStatusColor(card.status),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          card.status,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرصيد: ${card.balance} USD',
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: () => controller.removeCreditCard(card.id),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
