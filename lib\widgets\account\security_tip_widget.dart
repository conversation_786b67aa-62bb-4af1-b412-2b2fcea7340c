import 'package:flutter/material.dart';

/// Widget for displaying a security tip with icon
class SecurityTipWidget extends StatelessWidget {
  /// The text of the tip
  final String text;
  
  /// The icon to display
  final IconData icon;
  
  /// Optional icon color
  final Color? iconColor;

  /// Constructor
  const SecurityTipWidget({
    super.key,
    required this.text,
    required this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 16, color: iconColor ?? Colors.orange),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }
}
