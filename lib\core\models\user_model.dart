import 'dart:convert';
import 'package:get/get.dart';

import '../enums/app_enums.dart';

class UserModel {
  final User user;

  UserModel({required this.user});

  Map<String, dynamic> toJson() {
    return {'user': user.toJson()};
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    try {
      final userData = json['user'] ?? {};
      final accountData = userData['account'] ?? {};
      final documents = userData['documents'] ?? [];
      final contacts = userData['contacts'] ?? [];

      return UserModel(
        user: User(
          id: userData['id']?.toString() ?? '',
          fullName: userData['fullName']?.toString() ?? '',
          email: userData['email']?.toString() ?? '',
          phone: userData['phone']?.toString() ?? '',
          gender: userData['gender']?.toString() ?? '',
          nationality: userData['nationality']?.toString() ?? '',
          dateOfBirth: userData['dateOfBirth']?.toString() ?? '',
          kycStatus: KycStatus.values.firstWhere(
            (e) => e.toString().split('.').last == userData['kycStatus'],
            orElse: () => KycStatus.notStarted,
          ),
          verification: Verification.fromJson(userData['verification'] ?? {}),
          address: Address.fromJson(userData['address'] ?? {}),
          documents:
              documents is List
                  ? documents
                      .map((d) => Document.fromJson(d as Map<String, dynamic>))
                      .toList()
                  : [],
          account: Account.fromJson(accountData),
          preferences: Preferences.fromJson(userData['preferences'] ?? {}),
          notifications: Notifications.fromJson(
            userData['notifications'] ?? {},
          ),
          security: Security.fromJson(userData['security'] ?? {}),
          contacts:
              contacts is List
                  ? contacts
                      .map((c) => Contact.fromJson(c as Map<String, dynamic>))
                      .toList()
                  : [],
        ),
      );
    } catch (e) {
      Get.log('Error parsing UserModel: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      rethrow;
    }
  }
}

class User {
  final String id;
  final String fullName;
  final String email;
  final String phone;
  final String dateOfBirth;
  final String gender;
  final String nationality;
  final KycStatus kycStatus;
  final Verification verification;
  final Address address;
  final List<Document> documents;
  final Account? account; // Make account nullable
  final Preferences preferences;
  final Notifications notifications;
  final Security security;
  final List<Contact> contacts;

  User({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.dateOfBirth,
    required this.gender,
    required this.nationality,
    required this.kycStatus,
    required this.verification,
    required this.address,
    required this.documents,
    this.account, // Make account optional
    required this.preferences,
    required this.notifications,
    required this.security,
    required this.contacts,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'nationality': nationality,
      'kycStatus': kycStatus.toString().split('.').last,
      'verification': verification.toJson(),
      'address': address.toJson(),
      'documents': documents.map((doc) => doc.toJson()).toList(),
      'account': account?.toJson(), // Use null-aware operator
      'preferences': preferences.toJson(),
      'notifications': notifications.toJson(),
      'security': security.toJson(),
      'contacts': contacts.map((contact) => contact.toJson()).toList(),
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    try {
      final documents = json['documents'] ?? [];
      final contacts = json['contacts'] ?? [];

      return User(
        id: json['id']?.toString() ?? '',
        fullName: json['fullName']?.toString() ?? '',
        email: json['email']?.toString() ?? '',
        phone: json['phone']?.toString() ?? '',
        dateOfBirth: json['dateOfBirth']?.toString() ?? '',
        gender: json['gender']?.toString() ?? '',
        nationality: json['nationality']?.toString() ?? '',
        kycStatus: KycStatus.values.firstWhere(
          (e) => e.toString().split('.').last == json['kycStatus'],
          orElse: () => KycStatus.notStarted,
        ),
        verification: Verification.fromJson(json['verification'] ?? {}),
        address: Address.fromJson(json['address'] ?? {}),
        documents:
            documents is List
                ? documents
                    .map(
                      (doc) => Document.fromJson(doc as Map<String, dynamic>),
                    )
                    .toList()
                : [],
        account: Account.fromJson(json['account'] ?? {}),
        preferences: Preferences.fromJson(json['preferences'] ?? {}),
        notifications: Notifications.fromJson(json['notifications'] ?? {}),
        security: Security.fromJson(json['security'] ?? {}),
        contacts:
            contacts is List
                ? contacts
                    .map(
                      (contact) =>
                          Contact.fromJson(contact as Map<String, dynamic>),
                    )
                    .toList()
                : [],
      );
    } catch (e) {
      Get.log('Error parsing User: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      rethrow;
    }
  }
}

class Account {
  final String accountNumber;
  final String type;
  final String currency;
  final double balance;
  final String status;
  final DateTime createdAt;
  final DateTime lastLogin;
  final AccountLimits limits;
  final List<Transaction> transactions;
  final List<CreditCard> creditCards;

  Account({
    required this.accountNumber,
    required this.type,
    required this.currency,
    required this.balance,
    required this.status,
    required this.createdAt,
    required this.lastLogin,
    required this.limits,
    required this.transactions,
    required this.creditCards,
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    try {
      final transactions = json['transactions'] ?? [];
      final creditCards = json['creditCards'] ?? [];

      return Account(
        accountNumber: json['accountNumber']?.toString() ?? '',
        type: json['type']?.toString() ?? '',
        currency: json['currency']?.toString() ?? '',
        balance: (json['balance'] ?? 0).toDouble(),
        status: json['status']?.toString() ?? '',
        createdAt:
            json['createdAt'] != null
                ? DateTime.parse(json['createdAt'])
                : DateTime.now(),
        lastLogin:
            json['lastLogin'] != null
                ? DateTime.parse(json['lastLogin'])
                : DateTime.now(),
        limits: AccountLimits.fromJson(json['limits'] ?? {}),
        transactions:
            transactions is List
                ? transactions
                    .map(
                      (txn) =>
                          Transaction.fromJson(txn as Map<String, dynamic>),
                    )
                    .toList()
                : [],
        creditCards:
            creditCards is List
                ? creditCards
                    .map(
                      (card) =>
                          CreditCard.fromJson(card as Map<String, dynamic>),
                    )
                    .toList()
                : [],
      );
    } catch (e) {
      Get.log('Error parsing Account: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'accountNumber': accountNumber,
      'type': type,
      'currency': currency,
      'balance': balance,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin.toIso8601String(),
      'limits': limits.toJson(),
      'transactions': transactions.map((txn) => txn.toJson()).toList(),
      'creditCards': creditCards.map((card) => card.toJson()).toList(),
    };
  }
}

class Address {
  final String street;
  final String city;
  final String state;
  final String zipCode;
  final String country;

  Address({
    required this.street,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
  });

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
    };
  }

  factory Address.fromJson(Map<String, dynamic> json) {
    try {
      return Address(
        street: json['street']?.toString() ?? '',
        city: json['city']?.toString() ?? '',
        state: json['state']?.toString() ?? '',
        zipCode: json['zipCode']?.toString() ?? '',
        country: json['country']?.toString() ?? '',
      );
    } catch (e) {
      Get.log('Error parsing Address: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      return Address(street: '', city: '', state: '', zipCode: '', country: '');
    }
  }
}

class Document {
  final String id;
  final String type;
  final String number;
  final String status;
  final String uploadedAt;
  final String expiryDate;
  final String verifiedAt;
  final String issuedBy;
  final String countryCode;
  final String fileUrl;

  Document({
    required this.id,
    required this.type,
    required this.number,
    required this.status,
    required this.uploadedAt,
    required this.expiryDate,
    required this.verifiedAt,
    required this.issuedBy,
    required this.countryCode,
    required this.fileUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'number': number,
      'status': status,
      'uploadedAt': uploadedAt,
      'expiryDate': expiryDate,
      'verifiedAt': verifiedAt,
      'issuedBy': issuedBy,
      'countryCode': countryCode,
      'fileUrl': fileUrl,
    };
  }

  factory Document.fromJson(Map<String, dynamic> json) {
    return Document(
      id: json['id']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      number: json['number']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      uploadedAt: json['uploadedAt']?.toString() ?? '',
      expiryDate: json['expiryDate']?.toString() ?? '',
      verifiedAt: json['verifiedAt']?.toString() ?? '',
      issuedBy: json['issuedBy']?.toString() ?? '',
      countryCode: json['countryCode']?.toString() ?? '',
      fileUrl: json['fileUrl']?.toString() ?? '',
    );
  }
}

class Security {
  final TwoFactorAuth twoFactorAuth;
  final BiometricAuth biometricAuth;
  final List<Device> devices;

  Security({
    required this.twoFactorAuth,
    required this.biometricAuth,
    required this.devices,
  });

  Map<String, dynamic> toJson() {
    return {
      'twoFactorAuth': twoFactorAuth.toJson(),
      'biometricAuth': biometricAuth.toJson(),
      'devices': devices.map((d) => d.toJson()).toList(),
    };
  }

  factory Security.fromJson(Map<String, dynamic> json) {
    try {
      final devices = json['devices'] ?? [];

      return Security(
        twoFactorAuth: TwoFactorAuth.fromJson(json['twoFactorAuth'] ?? {}),
        biometricAuth: BiometricAuth.fromJson(json['biometricAuth'] ?? {}),
        devices:
            devices is List
                ? devices
                    .map((d) => Device.fromJson(d as Map<String, dynamic>))
                    .toList()
                : [],
      );
    } catch (e) {
      Get.log('Error parsing Security: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      return Security(
        twoFactorAuth: TwoFactorAuth(enabled: false, method: '', secretKey: ''),
        biometricAuth: BiometricAuth(enabled: false, method: ''),
        devices: [],
      );
    }
  }
}

class TwoFactorAuth {
  final bool enabled;
  final String method;
  final String secretKey;

  TwoFactorAuth({
    required this.enabled,
    required this.method,
    required this.secretKey,
  });

  Map<String, dynamic> toJson() {
    return {'enabled': enabled, 'method': method, 'secretKey': secretKey};
  }

  factory TwoFactorAuth.fromJson(Map<String, dynamic> json) {
    return TwoFactorAuth(
      enabled: json['enabled'] ?? false,
      method: json['method'] ?? '',
      secretKey: json['secretKey'] ?? '',
    );
  }
}

class BiometricAuth {
  final bool enabled;
  final String method;

  BiometricAuth({required this.enabled, required this.method});

  Map<String, dynamic> toJson() {
    return {'enabled': enabled, 'method': method};
  }

  factory BiometricAuth.fromJson(Map<String, dynamic> json) {
    return BiometricAuth(
      enabled: json['enabled'] ?? false,
      method: json['method'] ?? '',
    );
  }
}

class Device {
  final String id;
  final String name;
  final String os;
  final String appVersion;
  final String status;
  final List<Session> sessions;
  final List<ActivityLog> activityLogs;
  final List<SecurityLog> securityLogs;

  Device({
    required this.id,
    required this.name,
    required this.os,
    required this.appVersion,
    required this.status,
    required this.sessions,
    required this.activityLogs,
    required this.securityLogs,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'os': os,
      'appVersion': appVersion,
      'status': status,
      'sessions': sessions.map((s) => s.toJson()).toList(),
      'activityLogs': activityLogs.map((a) => a.toJson()).toList(),
      'securityLogs': securityLogs.map((s) => s.toJson()).toList(),
    };
  }

  factory Device.fromJson(Map<String, dynamic> json) {
    try {
      final sessions = json['sessions'] ?? [];
      final activityLogs = json['activityLogs'] ?? [];
      final securityLogs = json['securityLogs'] ?? [];

      return Device(
        id: json['id']?.toString() ?? '',
        name: json['name']?.toString() ?? '',
        os: json['os']?.toString() ?? '',
        appVersion: json['appVersion']?.toString() ?? '',
        status: json['status']?.toString() ?? '',
        sessions:
            sessions is List
                ? sessions
                    .map((s) => Session.fromJson(s as Map<String, dynamic>))
                    .toList()
                : [],
        activityLogs:
            activityLogs is List
                ? activityLogs
                    .map((a) => ActivityLog.fromJson(a as Map<String, dynamic>))
                    .toList()
                : [],
        securityLogs:
            securityLogs is List
                ? securityLogs
                    .map((s) => SecurityLog.fromJson(s as Map<String, dynamic>))
                    .toList()
                : [],
      );
    } catch (e) {
      Get.log('Error parsing Device: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      return Device(
        id: '',
        name: '',
        os: '',
        appVersion: '',
        status: '',
        sessions: [],
        activityLogs: [],
        securityLogs: [],
      );
    }
  }
}

class Session {
  final String id;
  final String ipAddress;
  final String location;
  final String status;
  final String createdAt;
  final String lastActivity;
  final String expiresAt;

  Session({
    required this.id,
    required this.ipAddress,
    required this.location,
    required this.status,
    required this.createdAt,
    required this.lastActivity,
    required this.expiresAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ipAddress': ipAddress,
      'location': location,
      'status': status,
      'createdAt': createdAt,
      'lastActivity': lastActivity,
      'expiresAt': expiresAt,
    };
  }

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      id: json['id'] ?? '',
      ipAddress: json['ipAddress'] ?? '',
      location: json['location'] ?? '',
      status: json['status'] ?? '',
      createdAt: json['createdAt'] ?? '',
      lastActivity: json['lastActivity'] ?? '',
      expiresAt: json['expiresAt'] ?? '',
    );
  }
}

class ActivityLog {
  final String id;
  final String timestamp;
  final String type;
  final String description;
  final String ipAddress;
  final String device;
  final String? referenceId;

  ActivityLog({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.description,
    required this.ipAddress,
    required this.device,
    this.referenceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp,
      'type': type,
      'description': description,
      'ipAddress': ipAddress,
      'device': device,
      'referenceId': referenceId,
    };
  }

  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'] ?? '',
      timestamp: json['timestamp'] ?? '',
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      ipAddress: json['ipAddress'] ?? '',
      device: json['device'] ?? '',
      referenceId: json['referenceId'],
    );
  }
}

class SecurityLog {
  final String timestamp;
  final String activity;
  final String location;
  final String ipAddress;

  SecurityLog({
    required this.timestamp,
    required this.activity,
    required this.location,
    required this.ipAddress,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp,
      'activity': activity,
      'location': location,
      'ipAddress': ipAddress,
    };
  }

  factory SecurityLog.fromJson(Map<String, dynamic> json) {
    return SecurityLog(
      timestamp: json['timestamp'] ?? '',
      activity: json['activity'] ?? '',
      location: json['location'] ?? '',
      ipAddress: json['ipAddress'] ?? '',
    );
  }
}

class Transaction {
  final String id;
  final String type;
  final double amount;
  final String currency;
  final String status;
  final String reference;
  final DateTime timestamp;
  final String description;
  final String? fromAccount;
  final String? toAccount;
  final double? fee;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.currency,
    required this.status,
    required this.reference,
    required this.timestamp,
    required this.description,
    this.fromAccount,
    this.toAccount,
    this.fee,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    try {
      return Transaction(
        id: json['id']?.toString() ?? '',
        type: json['type']?.toString() ?? '',
        amount: (json['amount'] ?? 0).toDouble(),
        currency: json['currency']?.toString() ?? '',
        status: json['status']?.toString() ?? '',
        reference: json['reference']?.toString() ?? '',
        timestamp:
            json['timestamp'] != null
                ? DateTime.parse(json['timestamp'])
                : DateTime.now(),
        description: json['description']?.toString() ?? '',
        fromAccount: json['fromAccount']?.toString(),
        toAccount: json['toAccount']?.toString(),
        fee: json['fee']?.toDouble(),
      );
    } catch (e) {
      Get.log('Error parsing Transaction: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'currency': currency,
      'status': status,
      'reference': reference,
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'fromAccount': fromAccount,
      'toAccount': toAccount,
      'fee': fee,
    };
  }
}

class Contact {
  final String account;
  final String name;

  Contact({required this.account, required this.name});

  Map<String, dynamic> toJson() {
    return {'account': account, 'name': name};
  }

  factory Contact.fromJson(Map<String, dynamic> json) {
    return Contact(account: json['account'] ?? '', name: json['name'] ?? '');
  }
}

class Verification {
  final bool emailVerified;
  final bool phoneVerified;

  Verification({required this.emailVerified, required this.phoneVerified});

  factory Verification.fromJson(Map<String, dynamic> json) {
    try {
      return Verification(
        emailVerified: json['emailVerified'] ?? false,
        phoneVerified: json['phoneVerified'] ?? false,
      );
    } catch (e) {
      Get.log('Error parsing Verification: $e', isError: true);
      Get.log('JSON data: ${jsonEncode(json)}', isError: true);
      return Verification(emailVerified: false, phoneVerified: false);
    }
  }

  Map<String, dynamic> toJson() {
    return {'emailVerified': emailVerified, 'phoneVerified': phoneVerified};
  }
}

class Preferences {
  final String preferredCurrency;
  final String language;
  final String timezone;

  Preferences({
    required this.preferredCurrency,
    required this.language,
    required this.timezone,
  });

  factory Preferences.fromJson(Map<String, dynamic> json) {
    return Preferences(
      preferredCurrency: json['preferredCurrency'] ?? 'USD',
      language: json['language'] ?? 'en-US',
      timezone: json['timezone'] ?? 'UTC',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preferredCurrency': preferredCurrency,
      'language': language,
      'timezone': timezone,
    };
  }
}

class Notifications {
  final bool push;
  final bool sms;
  final bool email;
  final Map<String, dynamic> data;

  Notifications({
    required this.push,
    required this.sms,
    required this.email,
    this.data = const {},
  });

  factory Notifications.fromJson(Map<String, dynamic> json) {
    // Create a copy of the json to avoid modifying the original
    final Map<String, dynamic> data = Map<String, dynamic>.from(json);

    // Remove the known fields to keep only the additional data
    data.remove('push');
    data.remove('sms');
    data.remove('email');

    return Notifications(
      push: json['push'] ?? false,
      sms: json['sms'] ?? false,
      email: json['email'] ?? false,
      data: data,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {
      'push': push,
      'sms': sms,
      'email': email,
    };

    // Add all additional data
    result.addAll(data);

    return result;
  }

  // Allow accessing data using the [] operator
  dynamic operator [](String key) => data[key];
}

class AccountLimits {
  final double dailyTransfer;
  final double minAmount;
  final double maxAmount;
  final double dailyWithdrawal;

  AccountLimits({required this.minAmount, required this.maxAmount, required this.dailyTransfer, required this.dailyWithdrawal});

  factory AccountLimits.fromJson(Map<String, dynamic> json) {
    return AccountLimits(
      minAmount: (json['minAmount'] ?? 0).toDouble(),
      maxAmount: (json['maxAmount'] ?? 0).toDouble(),
      dailyTransfer: (json['dailyTransfer'] ?? 0).toDouble(),
      dailyWithdrawal: (json['dailyWithdrawal'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'dailyTransfer': dailyTransfer, 'dailyWithdrawal': dailyWithdrawal};
  }
}

class CreditCard {
  final String id;
  final String type;
  final String status;
  final String maskedNumber;
  final String expiresAt;
  final double balance;
  final Address billingAddress;

  CreditCard({
    required this.id,
    required this.type,
    required this.status,
    required this.maskedNumber,
    required this.expiresAt,
    required this.balance,
    required this.billingAddress,
  });

  factory CreditCard.fromJson(Map<String, dynamic> json) {
    return CreditCard(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      status: json['status'] ?? '',
      maskedNumber: json['maskedNumber'] ?? '',
      expiresAt: json['expiresAt'] ?? '',
      balance: (json['balance'] ?? 0).toDouble(),
      billingAddress: Address.fromJson(json['billingAddress'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'status': status,
      'maskedNumber': maskedNumber,
      'expiresAt': expiresAt,
      'balance': balance,
      'billingAddress': billingAddress.toJson(),
    };
  }
}
