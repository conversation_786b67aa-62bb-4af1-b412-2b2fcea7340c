import 'package:flutter/material.dart';
import '../../constants/colors.dart';

class CustomBottomSheetTheme {
  /// --To Avoid Creating Instances
  CustomBottomSheetTheme._();

  /// -- Light Theme
  static BottomSheetThemeData light = BottomSheetThemeData(
    showDragHandle: true,
    backgroundColor: AppColors.light,
    modalBackgroundColor: AppColors.light,
    constraints: const BoxConstraints(minWidth: double.infinity),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16.0),
    ),
  );

  /// -- Light Theme
  static BottomSheetThemeData dark = BottomSheetThemeData(
    showDragHandle: true,
    backgroundColor: AppColors.dark,
    modalBackgroundColor: AppColors.dark,
    constraints: const BoxConstraints(minWidth: double.infinity),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
  );
}
