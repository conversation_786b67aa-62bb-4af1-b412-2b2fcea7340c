import 'package:flutter/material.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// A unified section header widget for consistent appearance across the app
class SectionHeaderWidget extends StatelessWidget {
  /// The title text
  final String title;

  /// Optional subtitle text
  final String? subtitle;

  /// Optional action widget (usually a button)
  final Widget? action;

  /// Optional title style
  final TextStyle? titleStyle;

  /// Optional subtitle style
  final TextStyle? subtitleStyle;

  /// Optional padding
  final EdgeInsetsGeometry? padding;

  /// Optional margin
  final EdgeInsetsGeometry? margin;

  /// Optional divider below the header
  final bool showDivider;

  /// Optional callback when the header is tapped
  final VoidCallback? onTap;

  /// Constructor
  const SectionHeaderWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.action,
    this.titleStyle,
    this.subtitleStyle,
    this.padding,
    this.margin,
    this.showDivider = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final header = Padding(
      padding: padding ?? EdgeInsets.symmetric(vertical: AppSizes.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style:
                      titleStyle ??
                      TextStyle(
                        fontSize: AppSizes.headlineSmall,
                        fontWeight: FontWeight.bold,
                        color: AppColors.lightTitleColor,
                      ),
                ),
                if (subtitle != null) ...[
                  SizedBox(height: AppSizes.xs),
                  Text(
                    subtitle!,
                    style:
                        subtitleStyle ??
                        TextStyle(
                          fontSize: AppSizes.bodySmall,
                          color: AppColors.greyColor,
                        ),
                  ),
                ],
              ],
            ),
          ),
          if (action != null) action!,
        ],
      ),
    );

    final content =
        showDivider ? Column(children: [header, const Divider()]) : header;

    if (onTap != null) {
      return InkWell(onTap: onTap, child: content);
    }

    return Container(margin: margin, child: content);
  }
}
