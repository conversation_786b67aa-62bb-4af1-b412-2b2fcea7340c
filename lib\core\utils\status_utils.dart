import 'package:flutter/material.dart';
import '../enums/app_enums.dart';

/// Utility class for status-related operations
class StatusUtils {
  /// Convert string status to TransactionStatus enum
  static TransactionStatus stringToTransactionStatus(String status) {
    try {
      return TransactionStatus.values.firstWhere(
        (e) => e.toString().split('.').last.toLowerCase() == status.toLowerCase(),
        orElse: () => TransactionStatus.pending,
      );
    } catch (e) {
      return TransactionStatus.pending;
    }
  }

  /// Get color for transaction status
  static Color getTransactionStatusColor(String status) {
    final transactionStatus = stringToTransactionStatus(status);
    
    switch (transactionStatus) {
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.failed:
        return Colors.red;
      case TransactionStatus.cancelled:
        return Colors.grey;
    }
  }
  
  /// Get color for bill status
  static Color getBillStatusColor(String status) {
    switch (status) {
      case 'مدفوعة':
        return Colors.green;
      case 'غير مدفوعة':
        return Colors.red;
      case 'متأخرة':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
  
  /// Get color for card status
  static Color getCardStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.grey;
      case 'blocked':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  /// Get Arabic label for transaction status
  static String getTransactionStatusLabel(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return 'مكتملة';
      case TransactionStatus.pending:
        return 'قيد الانتظار';
      case TransactionStatus.failed:
        return 'فشلت';
      case TransactionStatus.cancelled:
        return 'ملغاة';
    }
  }
  
  /// Get Arabic label for transaction status string
  static String getTransactionStatusLabelFromString(String status) {
    return getTransactionStatusLabel(stringToTransactionStatus(status));
  }
}
