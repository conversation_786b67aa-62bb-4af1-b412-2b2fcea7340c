import 'custom_theme/icon.dart';
import 'custom_theme/outlined_button.dart';
import 'custom_theme/elevated_button.dart';
import 'custom_theme/bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'custom_theme/text_field.dart';
import 'custom_theme/checkbox.dart';
import 'custom_theme/appbar.dart';
import '../constants/colors.dart';
import 'custom_theme/chip.dart';
import 'custom_theme/text.dart';

class AppTheme {
  AppTheme._();

  static ThemeData light = ThemeData(
      useMaterial3: true,
      //fontFamily: 'Binance',
      brightness: Brightness.light,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.light,

      textTheme: CustomTextTheme.light,
      chipTheme: CustomChipTheme.light,
      iconTheme: CustomIconTheme.light,
      appBarTheme: CustomAppBarTheme.light,
      checkboxTheme: CustomCheckboxTheme.light,
      bottomSheetTheme: CustomBottomSheetTheme.light,
      inputDecorationTheme: CustomTextFieldTheme.light,
      elevatedButtonTheme: CustomElevatedButtonTheme.light,
      outlinedButtonTheme: CustomOutlinedButtonTheme.light,
  );
  static ThemeData dark = ThemeData(
      useMaterial3: true,
      //fontFamily: 'Binance',
      brightness: Brightness.dark,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.dark,

      textTheme: CustomTextTheme.dark,
      chipTheme: CustomChipTheme.dark,
      iconTheme: CustomIconTheme.dark,
      appBarTheme: CustomAppBarTheme.dark,
      checkboxTheme: CustomCheckboxTheme.dark,
      bottomSheetTheme: CustomBottomSheetTheme.dark,
      inputDecorationTheme: CustomTextFieldTheme.dark,
      elevatedButtonTheme: CustomElevatedButtonTheme.dark,
      outlinedButtonTheme: CustomOutlinedButtonTheme.dark,
  );
}
