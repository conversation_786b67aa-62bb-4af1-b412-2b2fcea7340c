import 'package:flutter/material.dart';

import '../../utils/constants/colors.dart';
import '../../utils/helpers/helpers_functions.dart';

/// Widget for displaying a dropdown tile with icon
class DropdownTileWidget extends StatelessWidget {
  /// The title of the tile
  final String title;

  /// The icon to display
  final IconData? leadingIcon;

  /// The current selected value
  final String value;

  /// The list of available items
  final List<String> items;

  /// Callback when the selected value changes
  final ValueChanged<String?> onChanged;

  /// Constructor
  const DropdownTileWidget({
    super.key,
    required this.title,
    this.leadingIcon,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);

    return ListTile(
      leading:
          leadingIcon != null
              ? Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color:
                      dark ? AppColors.darkFillColor : AppColors.lightFillColor,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(leadingIcon),
              )
              : null,
      title: Text(title),
      trailing: DropdownButton<String>(
        value: items.contains(value) ? value : items.first,
        underline: const SizedBox(),
        items:
            items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item, style: TextStyle(fontSize: 14)),
              );
            }).toList(),
        onChanged: onChanged,
      ),
    );
  }
}
