import 'package:flutter/material.dart';
import '../../core/models/user_model.dart';
import '../../core/utils/app_formatters.dart';
import '../../core/utils/transaction_utils.dart';
import '../../utils/constants/sizes.dart';
import 'avatar_widget.dart';

/// A shared widget for displaying a transaction item
/// Used in both transaction history and home screens
class TransactionItemWidget extends StatelessWidget {
  /// The transaction to display
  final Transaction transaction;

  /// Function to call when the transaction is tapped
  final Function(Transaction) onTap;

  /// Optional function to get the status color
  final Color Function(String)? getStatusColor;

  /// Constructor
  const TransactionItemWidget({
    super.key,
    required this.transaction,
    required this.onTap,
    this.getStatusColor,
  });

  @override
  Widget build(BuildContext context) {
    // Normalize the transaction type string to match the enum
    final transactionType = TransactionUtils.stringToTransactionType(
      transaction.type,
    );

    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: AppSizes.defaultSpace),
      onTap: () => onTap(transaction),
      // leading: AvatarWidget(transactionType: transactionType),
      title: Text(
        TransactionUtils.getTransactionTypeLabel(transactionType),
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        AppFormatters.formatDateTime(transaction.timestamp),
        style: const TextStyle(fontSize: 12, color: Colors.grey),
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            AppFormatters.formatCurrency(
              transaction.amount,
              currencyCode: transaction.currency,
            ),
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(transaction.status, style: TextStyle(fontSize: 12)),
        ],
      ),
    );
  }
}
