import 'package:flutter/material.dart';
import '../../core/utils/app_formatters.dart';

/// Widget for displaying a date picker field
class DatePickerFieldWidget extends StatelessWidget {
  /// The label text
  final String label;
  
  /// The selected date
  final DateTime? date;
  
  /// Callback when date is selected
  final Function(DateTime?) onDateSelected;

  /// Constructor
  const DatePickerFieldWidget({
    super.key,
    required this.label,
    required this.date,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: Theme.of(context).primaryColor,
                ),
              ),
              child: child!,
            );
          },
        );

        if (pickedDate != null) {
          onDateSelected(pickedDate);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          prefixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          date != null ? AppFormatters.formatDate(date) : 'اختر التاريخ',
        ),
      ),
    );
  }
}
