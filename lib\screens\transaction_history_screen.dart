import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../common/widgets/empty_state_widget.dart';
import '../common/widgets/loading_widget.dart';
import '../common/widgets/search/search_bar_widget.dart';
import '../common/widgets/transaction_item_widget.dart';
import '../core/controllers/transaction_history_controller.dart';
import '../core/enums/app_enums.dart';
import '../core/utils/status_utils.dart';
import '../widgets/transaction_history/filter_dialog_widget.dart';
import '../widgets/transaction_history/filter_indicator_widget.dart';
import '../widgets/transaction_history/transaction_filter_info_bar.dart';

/// Screen for displaying transaction history
class TransactionHistoryScreen extends StatelessWidget {
  /// The controller for managing transaction history
  final TransactionHistoryController controller;

  /// Constructor
  const TransactionHistoryScreen({super.key, required this.controller});

  /// Factory constructor to create screen with controller
  factory TransactionHistoryScreen.create() {
    return TransactionHistoryScreen(
      controller: Get.put(TransactionHistoryController()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('History'),
        action: [
          FilterIndicatorWidget(
            controller: controller,
            onFilterPressed: () => FilterDialogWidget.show(context, controller),
          ),
        ],
      ),
      body: _buildBody(context, controller),
    );
  }

  /// Build body with transactions list
  Widget _buildBody(
    BuildContext context,
    TransactionHistoryController controller,
  ) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const LoadingWidget();
      }

      final bool hasActiveFilters = _hasActiveFilters(controller);

      return Column(
        children: [
          // Search bar
          SearchBarWidget(
            hintText: 'البحث في المعاملات',
            onChanged: controller.updateSearchQuery,
          ),

          // Show filter info bar if filters are active
          if (hasActiveFilters)
            TransactionFilterInfoBar(
              count: controller.filteredTransactions.length,
              onClearFilters: controller.resetFilters,
            ),

          // Transactions list or empty state
          Expanded(
            child:
                controller.filteredTransactions.isEmpty
                    ? _buildEmptyState(hasActiveFilters)
                    : _buildTransactionsList(controller),
          ),
        ],
      );
    });
  }

  /// Build the empty state widget based on whether filters are active.
  Widget _buildEmptyState(bool hasActiveFilters) {
    if (hasActiveFilters) {
      return const EmptyStateWidget(
        icon: Icons.search_off,
        title: 'لا توجد نتائج مطابقة',
        subtitle: 'حاول تعديل معايير البحث أو الفلترة',
      );
    }
    return const EmptyStateWidget(
      icon: Icons.history,
      title: 'لا توجد معاملات',
      subtitle: 'ستظهر معاملاتك هنا عندما تقوم بإجراء معاملات',
    );
  }

  /// Build the transactions list
  Widget _buildTransactionsList(TransactionHistoryController controller) {
    return ListView.builder(
      itemCount: controller.filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = controller.filteredTransactions[index];
        return TransactionItemWidget(
          transaction: transaction,
          onTap: controller.viewTransactionDetails,
          getStatusColor: StatusUtils.getTransactionStatusColor,
        );
      },
    );
  }

  /// Check if any filters are active.
  /// This is more robust than the previous implementation.
  bool _hasActiveFilters(TransactionHistoryController controller) {
    // Using .name is safer than .toString().split().last
    final isDefaultType =
        controller.filterType.value.toLowerCase() == TransactionType.all.name;

    return !isDefaultType ||
        controller.startDate.value != null ||
        controller.endDate.value != null ||
        controller.searchQuery.value.isNotEmpty;
  }
}






