import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../providers/user_provider.dart';

class ContactsController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final RxList<Contact> contacts = <Contact>[].obs;
  final RxBool isLoading = false.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadContacts();
    ever(searchQuery, (_) => filterContacts());
  }

  Future<void> loadContacts() async {
    try {
      isLoading.value = true;
      final user = userController.currentUser;
      if (user == null) return;

      contacts.clear();
      contacts.addAll(user.user.contacts.toList());
    } catch (e) {
      // Using GetX logging instead of print
      Get.log('Error loading contacts: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحميل جهات الاتصال',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  List<Contact> get filteredContacts {
    if (searchQuery.value.isEmpty) {
      return contacts;
    }
    final query = searchQuery.value.toLowerCase();
    return contacts
        .where(
          (contact) =>
              contact.name.toLowerCase().contains(query) ||
              contact.account.toLowerCase().contains(query),
        )
        .toList();
  }

  void filterContacts() {
    contacts.refresh();
  }

  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  Future<void> addContact(Contact contact) async {
    try {
      isLoading.value = true;
      final user = userController.currentUser;
      if (user == null) return;

      // Check if contact already exists
      if (contacts.any((c) => c.account == contact.account)) {
        Get.snackbar(
          'خطأ',
          'جهة الاتصال موجودة بالفعل',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      final updatedContacts = [...contacts, contact];
      await userController.updateContacts(updatedContacts);

      Get.snackbar(
        'نجاح',
        'تمت إضافة جهة الاتصال بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.log('Error adding contact: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في إضافة جهة الاتصال',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> removeContact(Contact contact) async {
    try {
      isLoading.value = true;
      final user = userController.currentUser;
      if (user == null) return;

      final updatedContacts =
          contacts.where((c) => c.account != contact.account).toList();
      await userController.updateContacts(updatedContacts);

      Get.snackbar(
        'نجاح',
        'تم حذف جهة الاتصال بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.log('Error removing contact: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في حذف جهة الاتصال',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateContact(Contact oldContact, Contact newContact) async {
    try {
      isLoading.value = true;
      final user = userController.currentUser;
      if (user == null) return;

      // Check if new account number already exists
      if (oldContact.account != newContact.account &&
          contacts.any((c) => c.account == newContact.account)) {
        Get.snackbar(
          'خطأ',
          'رقم الحساب موجود بالفعل',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      final updatedContacts =
          contacts
              .map((c) => c.account == oldContact.account ? newContact : c)
              .toList();

      await userController.updateContacts(updatedContacts);

      Get.snackbar(
        'نجاح',
        'تم تحديث جهة الاتصال بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.log('Error updating contact: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحديث جهة الاتصال',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
