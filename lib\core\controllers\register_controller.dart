import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../enums/app_enums.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';
import '../../routes/app_routes.dart';

class RegisterController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final fullNameController = TextEditingController();
  final phoneController = TextEditingController();

  final isLoading = false.obs;
  final obscurePassword = true.obs;
  final obscureConfirmPassword = true.obs;
  final acceptTerms = false.obs;

  late final UserController userController;

  @override
  void onInit() {
    super.onInit();
    userController = Get.find<UserController>();
  }

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    obscureConfirmPassword.value = !obscureConfirmPassword.value;
  }

  void toggleTerms() {
    acceptTerms.value = !acceptTerms.value;
  }

  Future<void> register() async {
    if (!formKey.currentState!.validate()) return;
    if (!acceptTerms.value) {
      Get.snackbar(
        'خطأ',
        'الرجاء الموافقة على شروط الخدمة وسياسة الخصوصية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;
      final newUser = UserModel(
        user: User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          fullName: fullNameController.text,
          email: emailController.text,
          phone: phoneController.text,
          gender: '',
          nationality: '',
          dateOfBirth: '',
          kycStatus: KycStatus.pending,
          verification: Verification(
            emailVerified: false,
            phoneVerified: false,
          ),
          preferences: Preferences(
            preferredCurrency: 'USD',
            language: Language.english.toString(),
            timezone: 'UTC',
          ),
          notifications: Notifications(push: true, sms: true, email: true),
          account: Account(
            accountNumber: DateTime.now().millisecondsSinceEpoch.toString(),
            type: AccountType.checking.toString(),
            currency: 'USD',
            balance: 0.0,
            status: 'active',
            createdAt: DateTime.now(),
            lastLogin: DateTime.now(),
            limits: AccountLimits(minAmount: 10.00, maxAmount: 1000000.00, dailyTransfer: 50000.00, dailyWithdrawal: 20000.00),
            transactions: [],
            creditCards: [],
          ),
          address: Address(
            street: '',
            city: '',
            state: '',
            zipCode: '',
            country: '',
          ),
          documents: [],
          contacts: [],
          security: Security(
            twoFactorAuth: TwoFactorAuth(
              enabled: false,
              method: '',
              secretKey: '',
            ),
            biometricAuth: BiometricAuth(enabled: false, method: ''),
            devices: [],
          ),
        ),
      );

      await userController.updateUser(newUser);
      Get.toNamed(
        AppRoutes.otpVerification,
        arguments: {
          'email': emailController.text,
          'phone': phoneController.text,
        },
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء الحساب. الرجاء المحاولة مرة أخرى.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    fullNameController.dispose();
    phoneController.dispose();
    super.onClose();
  }
}
