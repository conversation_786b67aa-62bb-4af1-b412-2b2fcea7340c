import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/notifications_controller.dart';
import '../widgets/notifications/notification_item_widget.dart';
import '../widgets/notifications/notifications_empty_widget.dart';
import '../widgets/notifications/notifications_loading_widget.dart';

/// Screen for displaying user notifications
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Try to find existing controller or create a new one if not found
    late final NotificationsController controller;
    try {
      controller = Get.find<NotificationsController>();
    } catch (e) {
      // If controller is not found, create it
      controller = Get.put(NotificationsController());
    }

    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Notifications'),
        action: [
          Obx(
            () => Badge(
              isLabelVisible: controller.unreadCount > 0,
              label: Text('${controller.unreadCount}'),
              child: IconButton(
                icon: const Icon(Icons.done_all),
                onPressed: () async {
                  await controller.markAllAsRead();
                },
                tooltip: 'تعليم الكل كمقروء',
              ),
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const NotificationsLoadingWidget();
        }

        if (controller.notifications.isEmpty) {
          return const NotificationsEmptyWidget();
        }

        return ListView.builder(
          itemCount: controller.notifications.length,
          itemBuilder: (context, index) {
            final notification = controller.notifications[index];
            return NotificationItemWidget(
              notification: notification,
              controller: controller,
            );
          },
        );
      }),
    );
  }
}
