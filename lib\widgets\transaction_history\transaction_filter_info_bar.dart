import 'package:flutter/material.dart';

/// Widget for displaying filter information bar in transaction history
class TransactionFilterInfoBar extends StatelessWidget {
  /// Number of transactions found
  final int count;
  
  /// Callback when clear filters button is pressed
  final VoidCallback onClearFilters;

  /// Constructor
  const TransactionFilterInfoBar({
    super.key,
    required this.count,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).primaryColor.withAlpha(25),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'تم العثور على $count معاملة',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          TextButton.icon(
            onPressed: onClearFilters,
            icon: const Icon(Icons.clear, size: 16),
            label: const Text('مسح الفلاتر'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              minimumSize: const Size(0, 0),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }
}
