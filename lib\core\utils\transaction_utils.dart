import '../enums/app_enums.dart';
import 'package:flutter/material.dart';

/// Utility class for transaction-related operations
class TransactionUtils {
  /// API transaction types mapping to our enum types
  static const Map<String, String> _apiTypeMapping = {
    'sent': 'send',
    'received': 'receive',
    'deposit': 'topup',
    'withdrawal': 'withdraw',
  };

  /// Transaction type colors
  static const Map<TransactionType, Color> _typeColors = {
    TransactionType.send: Colors.red,
    TransactionType.receive: Colors.green,
    TransactionType.topup: Colors.blue,
    TransactionType.withdraw: Colors.orange,
    TransactionType.all: Colors.grey,
  };

  /// Transaction type icons
  static const Map<TransactionType, IconData> _typeIcons = {
    TransactionType.send: Icons.arrow_upward,
    TransactionType.receive: Icons.arrow_downward,
    TransactionType.topup: Icons.add,
    TransactionType.withdraw: Icons.remove,
    TransactionType.all: Icons.swap_horiz,
  };

  /// Transaction type Arabic labels
  static const Map<TransactionType, String> _typeLabels = {
    TransactionType.all: 'All',
    TransactionType.send: 'Sent',
    TransactionType.receive: 'Received',
    TransactionType.topup: 'Deposited',
    TransactionType.withdraw: 'Withdraw',
  };

  /// Convert TransactionType enum to its string representation
  static String transactionTypeToString(TransactionType type) {
    return type.toString().split('.').last.toLowerCase();
  }

  /// Convert string to TransactionType enum
  static TransactionType stringToTransactionType(String typeStr) {
    try {
      // Map API transaction types to our enum types
      final normalizedType = _normalizeTransactionType(typeStr);

      return TransactionType.values.firstWhere(
        (t) =>
            t.toString().split('.').last.toLowerCase() ==
            normalizedType.toLowerCase(),
        orElse: () => TransactionType.all,
      );
    } catch (e) {
      return TransactionType.all;
    }
  }

  /// Normalize transaction type strings from API to match our enum
  static String _normalizeTransactionType(String type) {
    final lowerType = type.toLowerCase();
    return _apiTypeMapping[lowerType] ?? lowerType;
  }

  /// Get transaction color based on type
  static Color getTransactionColor(TransactionType type) {
    return _typeColors[type] ?? Colors.grey;
  }

  /// Get transaction color based on type string
  static Color getTransactionColorFromString(String typeStr) {
    return getTransactionColor(stringToTransactionType(typeStr));
  }

  /// Get transaction icon based on type
  static IconData getTransactionIcon(TransactionType type) {
    return _typeIcons[type] ?? Icons.swap_horiz;
  }

  /// Get transaction icon based on type string
  static IconData getTransactionIconFromString(String typeStr) {
    return getTransactionIcon(stringToTransactionType(typeStr));
  }

  /// Get Arabic label for transaction type
  static String getTransactionTypeLabel(TransactionType type) {
    return _typeLabels[type] ?? type.toString().split('.').last;
  }

  /// Get Arabic label for transaction type string
  static String getTransactionTypeLabelFromString(String typeStr) {
    return getTransactionTypeLabel(stringToTransactionType(typeStr));
  }
}
