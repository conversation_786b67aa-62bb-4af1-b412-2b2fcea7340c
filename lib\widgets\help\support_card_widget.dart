import 'package:flutter/material.dart';

/// Widget for displaying a support card with icon and tap action
class SupportCardWidget extends StatelessWidget {
  /// The title of the card
  final String title;
  
  /// The subtitle of the card
  final String subtitle;
  
  /// The icon to display
  final IconData icon;
  
  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Constructor
  const SupportCardWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Theme.of(context).colorScheme.primary),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
}
