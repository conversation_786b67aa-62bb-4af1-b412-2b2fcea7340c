import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../common/widgets/shared_widgets.dart';
import '../core/controllers/settings_controller.dart';
import '../widgets/account/status_tile_widget.dart';
import '../widgets/settings/dropdown_tile_widget.dart';
// import '../widgets/settings/setting_tile_widget.dart';
import '../widgets/settings/switch_tile_widget.dart';

/// Screen for displaying and managing application settings
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SettingsController());

    return Scaffold(
      appBar: BuildAppBar(showBackArrow: true, title: const Text('Settings')),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Settings
            const SectionHeaderWidget(title: 'Account'),
            StatusTileWidget(
              title: 'Account Settings',
              leadingIcon: Icons.security,
              onTap: () => Get.toNamed('/account-settings'),
              trailingIcon: Icons.arrow_forward_ios,
            ),

            // App
            const SectionHeaderWidget(title: 'App'),
            Obx(
              () => DropdownTileWidget(
                title: 'Theme Mode',
                leadingIcon: Icons.dark_mode,
                value: controller.themeModeString,
                items: controller.themeModes,
                onChanged: controller.setThemeMode,
              ),
            ),
            Obx(
              () => DropdownTileWidget(
                title: 'App Language',
                leadingIcon: Icons.language,
                value: controller.selectedLanguage,
                items: controller.languages,
                onChanged: controller.setLanguage,
              ),
            ),

            // Notifications
            const SectionHeaderWidget(
              title: 'Notifications',
              showDivider: true,
            ),
            Obx(
              () => SwitchTileWidget(
                title: 'Push Notifications',
                leadingIcon: Icons.notifications,
                value: controller.notificationsEnabled.value,
                onChanged: controller.toggleNotifications,
              ),
            ),

            // Currency
            const SectionHeaderWidget(title: 'Currency', showDivider: true),
            Obx(
              () => DropdownTileWidget(
                title: 'Default Currency',
                leadingIcon: Icons.attach_money,
                value: controller.selectedCurrencyString,
                items: controller.currencies,
                onChanged: controller.setCurrency,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
