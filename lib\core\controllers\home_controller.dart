import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';
import '../utils/data_utils.dart';
import '../utils/navigation_utils.dart';
import '../utils/transaction_utils.dart';

/// Controller responsible for managing home screen
class HomeController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final currentIndex = 0.obs;
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  /// Load initial data
  Future<void> loadData() async {
    isLoading.value = true;
    try {
      await userController.loadUsers();
    } finally {
      isLoading.value = false;
    }
  }

  /// Change bottom navigation index
  void changeIndex(int index) {
    currentIndex.value = index;
  }

  /// Refresh data
  Future<void> refreshData() async {
    isLoading.value = true;
    try {
      await userController.loadUsers();
    } finally {
      isLoading.value = false;
    }
  }

  /// Navigate to send money screen
  void navigateToSendMoney() {
    NavigationUtils.navigateToSendMoney();
  }

  /// Navigate to transfer screen
  void navigateToTransfer() {
    NavigationUtils.navigateToTransfer();
  }

  /// Navigate to add contact screen
  void navigateToAddContact() {
    NavigationUtils.navigateToContacts();
  }

  /// Navigate to withdraw funds screen
  void navigateToWithdrawFunds() {
    NavigationUtils.navigateToWithdrawFunds();
  }

  /// Navigate to transaction history screen
  void navigateToTransactionHistory() {
    NavigationUtils.navigateToTransactionHistory();
  }

  /// Navigate to top up wallet (deposit) screen
  void navigateToTopUpWallet() {
    NavigationUtils.navigateToTopUpWallet();
  }

  /// Navigate to bills screen
  void navigateToBills() {
    NavigationUtils.navigateToBills();
  }

  /// Navigate to account screen
  void navigateToAccount() {
    NavigationUtils.navigateToAccount();
  }

  /// Navigate to credit cards screen
  void navigateToCreditCards() {
    NavigationUtils.navigateToCreditCards();
  }

  /// Navigate to transaction details
  void navigateToTransactionDetails(Transaction transaction) {
    NavigationUtils.navigateToTransactionDetails(transaction);
  }

  /// Get transaction color based on type
  Color getTransactionColor(String type) {
    return TransactionUtils.getTransactionColorFromString(type);
  }

  /// Get transaction icon based on type
  IconData getTransactionIcon(String type) {
    return TransactionUtils.getTransactionIconFromString(type);
  }

  /// Get recent transactions (up to 3)
  List<Transaction> getRecentTransactions() {
    final user = userController.currentUser;
    if (user == null || user.user.account?.transactions == null) {
      return [];
    }

    final transactions = user.user.account!.transactions;
    if (transactions.isEmpty) {
      return [];
    }

    // Use DataUtils to get recent transactions
    return DataUtils.getRecentTransactions(transactions, 3);
  }
}
