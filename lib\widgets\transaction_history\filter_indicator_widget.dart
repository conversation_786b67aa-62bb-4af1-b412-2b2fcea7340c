import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/controllers/transaction_history_controller.dart';
import '../../core/enums/app_enums.dart';

/// Widget for displaying a filter indicator in the app bar
class FilterIndicatorWidget extends StatelessWidget {
  /// The controller
  final TransactionHistoryController controller;

  /// Callback when filter button is pressed
  final VoidCallback onFilterPressed;

  /// Constructor
  const FilterIndicatorWidget({
    super.key,
    required this.controller,
    required this.onFilterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final bool hasActiveFilters = _hasActiveFilters();

      return Stack(
        alignment: Alignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
          ),
          if (hasActiveFilters)
            Positioned(
              top: 10,
              right: 10,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      );
    });
  }

  /// Check if any filters are active
  bool _hasActiveFilters() {
    return controller.filterType.value !=
            TransactionType.all.toString().split('.').last ||
        controller.startDate.value != null ||
        controller.endDate.value != null;
  }
}
