import 'package:intl/intl.dart';

/// Utility class for formatting data in the application
class AppFormatters {
  /// Date and time formatters
  static final DateFormat dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm:ss');
  static final DateFormat dateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat timeFormat = DateFormat('HH:mm:ss');
  static final DateFormat shortTimeFormat = DateFormat('HH:mm');
  static final DateFormat monthYearFormat = DateFormat('MMMM yyyy');

  /// Currency formatters
  static NumberFormat currencyFormat({
    String symbol = '',
    int decimalDigits = 2,
  }) {
    return NumberFormat.currency(symbol: symbol, decimalDigits: decimalDigits);
  }

  /// Number formatters
  static final NumberFormat decimalFormat = NumberFormat.decimalPattern();
  static final NumberFormat percentFormat = NumberFormat.percentPattern();

  /// Format date to string
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return dateTimeFormat.format(dateTime);
  }

  /// Format date only to string
  static String formatDate(DateTime? dateTime) {
    if (dateTime == null) return '';
    return dateFormat.format(dateTime);
  }

  /// Format time only to string
  static String formatTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return timeFormat.format(dateTime);
  }

  /// Format short time to string
  static String formatShortTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return shortTimeFormat.format(dateTime);
  }

  /// Format month and year to string
  static String formatMonthYear(DateTime? dateTime) {
    if (dateTime == null) return '';
    return monthYearFormat.format(dateTime);
  }

  /// Format currency value to string
  static String formatCurrency(
    double? value, {
    String symbol = '',
    String currencyCode = '',
  }) {
    if (value == null) return '';
    final formatter = currencyFormat(symbol: symbol);
    return '${formatter.format(value)}${currencyCode.isNotEmpty ? ' $currencyCode' : ''}';
  }

  /// Format decimal value to string
  static String formatDecimal(double? value) {
    if (value == null) return '';
    return decimalFormat.format(value);
  }

  /// Format percent value to string
  static String formatPercent(double? value) {
    if (value == null) return '';
    return percentFormat.format(value / 100);
  }

  /// Format phone number to readable format
  static String formatPhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) return '';

    // Remove any non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

    // Format based on length
    if (digitsOnly.length == 10) {
      return '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    } else if (digitsOnly.length > 10) {
      return '+${digitsOnly.substring(0, digitsOnly.length - 10)} ${digitsOnly.substring(digitsOnly.length - 10, digitsOnly.length - 7)}-${digitsOnly.substring(digitsOnly.length - 7, digitsOnly.length - 4)}-${digitsOnly.substring(digitsOnly.length - 4)}';
    }

    // Return as is if it doesn't match expected formats
    return phoneNumber;
  }

  /// Format account number with masking
  static String formatAccountNumber(String? accountNumber) {
    if (accountNumber == null || accountNumber.isEmpty) return '';

    if (accountNumber.length <= 4) return accountNumber;

    // Mask all but last 4 digits
    final lastFour = accountNumber.substring(accountNumber.length - 4);
    final masked = '*' * (accountNumber.length - 4);

    return '$masked$lastFour';
  }
}
