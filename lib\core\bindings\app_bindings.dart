import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../../features/auth/presentation/controllers/auth_controller.dart';
import '../controllers/send_money_controller.dart';
import '../controllers/register_controller.dart';
import '../controllers/profile_controller.dart';
import '../controllers/contacts_controller.dart';
import '../controllers/currency_exchange_controller.dart';
import '../controllers/receive_money_controller.dart';
import '../controllers/account_controller.dart';
import '../controllers/home_controller.dart';
import '../controllers/settings_controller.dart';
import '../controllers/transaction_details_controller.dart';
import '../controllers/transaction_history_controller.dart';
import '../controllers/account_settings_controller.dart';
import '../controllers/notifications_controller.dart';
import '../controllers/otp_verification_controller.dart';
import '../controllers/top_up_wallet_controller.dart';
import '../controllers/withdraw_funds_controller.dart';
import '../controllers/splash_controller.dart';
import '../controllers/credit_cards_controller.dart';
import '../controllers/bills_controller.dart';
import '../services/notification_service.dart';

class AppBindings extends Bindings {
  @override
  void dependencies() {
    // Initialize UserController first as it's used by other controllers
    Get.put(UserController(), permanent: true);

    // Initialize SettingsController early as it's needed for theme
    Get.put(SettingsController(), permanent: true);

    // Initialize NotificationService after UserController
    Future.delayed(const Duration(milliseconds: 100), () {
      Get.put(NotificationService(), permanent: true);
    });

    // Initialize other controllers
    Get.lazyPut(() => AuthController(), fenix: true);
    Get.lazyPut(() => SendMoneyController(), fenix: true);
    Get.lazyPut(() => RegisterController(), fenix: true);
    Get.lazyPut(() => ProfileController(), fenix: true);
    Get.lazyPut(() => ContactsController(), fenix: true);
    Get.lazyPut(() => CurrencyExchangeController(), fenix: true);
    Get.lazyPut(() => ReceiveMoneyController(), fenix: true);
    Get.lazyPut(() => AccountController(), fenix: true);
    Get.lazyPut(() => HomeController(), fenix: true);
    Get.lazyPut(() => TransactionDetailsController(), fenix: true);
    Get.lazyPut(() => TransactionHistoryController(), fenix: true);
    Get.lazyPut(() => AccountSettingsController(), fenix: true);
    Get.lazyPut(() => NotificationsController(), fenix: true);
    Get.lazyPut(() => OTPVerificationController(), fenix: true);
    Get.lazyPut(() => TopUpWalletController(), fenix: true);
    Get.lazyPut(() => WithdrawFundsController(), fenix: true);
    Get.lazyPut(() => SplashController(), fenix: true);
    Get.lazyPut(() => CreditCardsController(), fenix: true);
    Get.lazyPut(() => BillsController(), fenix: true);
  }
}
