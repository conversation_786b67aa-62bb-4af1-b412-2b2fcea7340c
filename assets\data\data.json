[{"user": {"id": "US001", "fullName": "<PERSON>", "email": "<EMAIL>", "phone": "+*********", "password": "123456", "dateOfBirth": "1990-05-15", "gender": "male", "nationality": "USA", "kycStatus": "verified", "verification": {"emailVerified": true, "phoneVerified": true}, "address": {"street": "123 Main St", "city": "Anytown", "state": "CA", "zipCode": "12345", "country": "USA"}, "documents": [{"id": "DOC001", "type": "passport", "number": "P000001", "status": "approved", "uploadedAt": "2023-06-15T03:51:45Z", "expiryDate": "2033-06-15T12:00:00Z", "verifiedAt": "2023-06-16T07:21:15Z", "issuedBy": "US Department of State", "countryCode": "US", "fileUrl": "https://example.com/documents/passport123.pdf"}], "account": {"accountNumber": "000001", "type": "checking", "currency": "USD", "balance": 10000, "status": "active", "createdAt": "2023-01-15T03:51:45Z", "lastLogin": "2024-03-19T02:43:12Z", "limits": {"minAmount": 10, "maxAmount": 1000000.0, "dailyTransfer": ********.0, "dailyWithdrawal": 2000000.0}, "transactions": [{"id": "TXN001", "type": "deposit", "amount": 1500, "currency": "USD", "status": "completed", "reference": "REF001", "timestamp": "2024-02-15T09:30:03Z", "description": "Monthly salary deposit"}, {"id": "TXN002", "type": "received", "amount": 50, "currency": "USD", "status": "completed", "fromAccount": "000002", "toAccount": "000001", "reference": "REF002", "timestamp": "2024-03-01T10:45:34Z", "description": "Transfer to <PERSON>", "fee": 2}, {"id": "TXN003", "type": "sent", "amount": 75.5, "currency": "USD", "status": "completed", "fromAccount": "000001", "toAccount": "000002", "reference": "REF003", "timestamp": "2024-03-05T11:20:26Z", "timezone": "America/New_York", "description": "Payment for dinner", "fee": 1}, {"id": "TXN004", "type": "withdrawal", "amount": 200, "currency": "USD", "status": "completed", "toAccount": "000001", "reference": "REF004", "timestamp": "2024-03-10T14:15:01Z", "description": "ATM withdrawal", "fee": 3}], "creditCards": [{"id": "CC001", "type": "Visa", "status": "active", "maskedNumber": "1234 5678 9012 3456", "expiresAt": "11/28", "cvv": 123, "balance": 5000, "billingAddress": {"street": "123 Main St", "city": "Anytown", "state": "CA", "zipCode": "12345", "country": "USA"}}]}, "preferences": {"preferredCurrency": "USD", "language": "en-US", "timezone": "America/New_York"}, "notifications": {"push": true, "sms": true, "email": true, "items": [{"id": "NOTIF001", "title": "تحويل جديد", "message": "تم استلام 50 دولار من أميت كومار", "timestamp": "2024-03-19T10:45:52Z", "type": "transaction", "isRead": false, "referenceId": "TXN002"}, {"id": "NOTIF002", "title": "تنبيه أمني", "message": "تم تسجيل الدخول من جهاز جديد", "timestamp": "2024-03-19T05:30:00Z", "type": "security", "isRead": true, "referenceId": null}, {"id": "NOTIF003", "title": "تحديث الحساب", "message": "تم تحديث ملفك الشخصي بنجاح", "timestamp": "2024-03-18T14:20:00Z", "type": "account", "isRead": true, "referenceId": null}, {"id": "NOTIF004", "title": "عرض ترويجي", "message": "احصل على خصم 10% على التحويلات الدولية حتى نهاية الشهر", "timestamp": "2024-03-17T09:15:00Z", "type": "promotion", "isRead": false, "referenceId": null}, {"id": "NOTIF005", "title": "سحب ناجح", "message": "تم سحب 200 دولار من حسابك", "timestamp": "2024-03-10T14:15:00Z", "type": "transaction", "isRead": true, "referenceId": "TXN004"}]}, "security": {"twoFactorAuth": {"enabled": true, "method": "app", "secretKey": "34T65RDF67990008"}, "biometricAuth": {"enabled": true, "method": "fingerprint"}, "devices": [{"id": "DEV001", "name": "iPhone 12", "os": "iOS 15.0", "appVersion": "2.1.0", "status": "active", "sessions": [{"id": "SESS001", "ipAddress": "***********", "location": "Anytown, CA, USA", "status": "active", "createdAt": "2024-03-19T05:25:00Z", "lastActivity": "2024-03-06T14:30:00Z", "expiresAt": "2024-03-20T14:25:00Z"}], "activityLogs": [{"id": "ACT001", "timestamp": "2024-03-01T10:00:00Z", "type": "login", "description": "User logged in from web interface", "ipAddress": "***********0", "device": "Chrome on Windows"}, {"id": "ACT002", "timestamp": "2024-03-05T11:30:00Z", "type": "transfer", "description": "User transferred $75.5 to account 000002", "referenceId": "TXN003"}, {"id": "ACT003", "timestamp": "2024-03-10T14:20:00Z", "type": "withdrawal", "description": "ATM withdrawal of $200", "referenceId": "TXN004"}], "securityLogs": [{"timestamp": "2024-03-19T05:30:00Z", "activity": "Suspicious login attempt detected", "location": "Anytown, CA, USA", "ipAddress": "***********"}]}]}, "contacts": [{"account": "000002", "name": "<PERSON><PERSON>"}, {"account": "000003", "name": "<PERSON>"}, {"account": "000004", "name": "<PERSON>"}, {"account": "000005", "name": "<PERSON><PERSON>"}, {"account": "000006", "name": "<PERSON>"}, {"account": "000007", "name": "<PERSON>"}, {"account": "000008", "name": "<PERSON>"}, {"account": "000009", "name": "<PERSON><PERSON>"}, {"account": "000010", "name": "<PERSON>"}]}}]