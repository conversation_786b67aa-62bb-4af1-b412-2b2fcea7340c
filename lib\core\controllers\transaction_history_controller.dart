import 'package:get/get.dart';
import '../enums/app_enums.dart';
import '../models/user_model.dart';
import '../providers/user_provider.dart';
import '../utils/app_formatters.dart';
import '../utils/data_utils.dart';
import '../utils/navigation_utils.dart';

/// Controller responsible for managing transaction history
class TransactionHistoryController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final RxList<Transaction> transactions = <Transaction>[].obs;
  final RxList<Transaction> filteredTransactions = <Transaction>[].obs;
  final RxBool isLoading = false.obs;
  final RxString filterType =
      TransactionType.all.toString().split('.').last.obs;

  // Filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadTransactions();
    ever(searchQuery, (_) => _applyFilters());
  }

  /// Update search query and apply filters
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Load transactions from user data
  void loadTransactions() {
    isLoading.value = true;

    try {
      final user = userController.currentUser;
      if (user != null && user.user.account?.transactions != null) {
        // Get transactions and sort them by date (newest first)
        transactions.value = DataUtils.sortTransactionsByDate(
          user.user.account!.transactions,
        );
        // Initialize filtered transactions with all transactions
        filteredTransactions.value = transactions;
      } else {
        transactions.value = [];
        filteredTransactions.value = [];
      }
    } catch (e) {
      Get.log('Error loading transactions: $e');
      transactions.value = [];
      filteredTransactions.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  /// Filter transactions by multiple criteria
  void filterTransactions(
    String type, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    // Update filter values
    filterType.value = type;
    if (startDate != null) this.startDate.value = startDate;
    if (endDate != null) this.endDate.value = endDate;

    // Apply filters
    _applyFilters();
  }

  /// Apply all current filters to the transactions
  void _applyFilters() {
    isLoading.value = true;

    try {
      // Start with all transactions
      List<Transaction> filtered = List.from(transactions);

      // Debug information
      Get.log('Filtering transactions with criteria:');
      Get.log('Type: ${filterType.value}');
      Get.log(
        'Start date: ${startDate.value != null ? AppFormatters.formatDate(startDate.value) : "null"}',
      );
      Get.log(
        'End date: ${endDate.value != null ? AppFormatters.formatDate(endDate.value) : "null"}',
      );
      Get.log('Available transactions: ${filtered.length}');

      // Apply filters using DataUtils

      // Filter by type
      filtered = DataUtils.filterTransactionsByType(filtered, filterType.value);
      Get.log('After type filter: ${filtered.length}');

      // Filter by date range
      filtered = DataUtils.filterTransactionsByDateRange(
        filtered,
        startDate.value,
        endDate.value,
      );
      Get.log('After date filter: ${filtered.length}');

      // Filter by search query
      if (searchQuery.value.isNotEmpty) {
        filtered = DataUtils.filterTransactionsBySearchQuery(
          filtered,
          searchQuery.value,
        );
        Get.log('After search filter: ${filtered.length}');
      }

      // Update the filtered transactions list
      filteredTransactions.value = filtered;
      Get.log('Final filtered transactions: ${filteredTransactions.length}');
    } catch (e) {
      Get.log('Error applying filters: $e');
      filteredTransactions.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  /// Reset all filters
  void resetFilters() {
    filterType.value = TransactionType.all.toString().split('.').last;
    startDate.value = null;
    endDate.value = null;
    searchQuery.value = '';

    // Reset filtered transactions to all transactions
    filteredTransactions.value = transactions;
  }

  /// Navigate to transaction details
  void viewTransactionDetails(Transaction transaction) {
    NavigationUtils.navigateToTransactionDetails(transaction);
  }
}
