import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/providers/user_provider.dart';
import '../core/controllers/account_settings_controller.dart';
import '../widgets/account_settings_widgets.dart';

/// Screen for managing account security settings
class AccountSettingsScreen extends StatelessWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AccountSettingsController>();
    final userController = Get.find<UserController>();
    final user = userController.currentUser;

    if (user == null) {
      return const Scaffold(body: Center(child: Text('الرجاء تسجيل الدخول')));
    }

    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Account Settings'),
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : ListView(
                  children: [
                    buildStatusTile(
                      "KYC",
                      () => Get.toNamed('/kyc'),
                      status: user.user.kycStatus.toString().split('.').last,
                      leadingIcon: Icons.verified_user,
                    ),
                    buildStatusTile(
                      "البريد الإلكتروني",
                      () => Get.toNamed('/bind-email'),
                      status:
                          user.user.verification.emailVerified
                              ? "تم التحقق"
                              : "لم يتم التحقق",
                      leadingIcon: Icons.email,
                    ),
                    buildStatusTile(
                      "رقم الهاتف",
                      () => Get.toNamed('/bind-phone-number'),
                      status:
                          user.user.verification.phoneVerified
                              ? "تم التحقق"
                              : "لم يتم التحقق",
                      leadingIcon: Icons.phone_android,
                    ),
                    buildStatusTile(
                      "تغيير رمز PIN",
                      () => Get.toNamed('/modify-pin-code'),
                      leadingIcon: Icons.pin,
                    ),

                    const SizedBox(height: 32),
                    // Devices
                    const Text(
                      'الأجهزة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...user.user.security.devices.map(
                      (device) => buildDeviceTile(device),
                    ),

                    const SizedBox(height: 32),

                    // Security Tips
                    buildSecurityTipsCard(),
                  ],
                ),
      ),
    );
  }
}
