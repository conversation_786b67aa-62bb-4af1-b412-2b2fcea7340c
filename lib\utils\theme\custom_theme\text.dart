import 'package:flutter/material.dart';

import '../../constants/colors.dart';
import '../../constants/sizes.dart';

class CustomTextTheme {
  CustomTextTheme._();
static TextTheme light = TextTheme(
  headlineLarge: const TextStyle().copyWith(fontSize: AppSizes.headlineLarge, fontWeight: FontWeight.bold, color: AppColors.lightTitleColor),
  headlineMedium: const TextStyle().copyWith(fontSize: AppSizes.headlineMedium, fontWeight: FontWeight.w600, color: AppColors.lightTitleColor),
  headlineSmall: const TextStyle().copyWith(fontSize: AppSizes.headlineSmall, fontWeight: FontWeight.w600, color: AppColors.lightTitleColor),

  titleLarge: const TextStyle().copyWith(fontSize: AppSizes.titleLarge, fontWeight: FontWeight.w600, color: AppColors.lightTitleColor),
  titleMedium: const TextStyle().copyWith(fontSize: AppSizes.titleMedium, fontWeight: FontWeight.w500, color: AppColors.lightTitleColor),
  titleSmall: const TextStyle().copyWith(fontSize: AppSizes.titleSmall, fontWeight: FontWeight.w400, color: AppColors.lightTitleColor),

  bodyLarge: const TextStyle().copyWith(fontSize: AppSizes.bodyLarge, fontWeight: FontWeight.w500, color: AppColors.lightTitleColor),
  bodyMedium: const TextStyle().copyWith(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor),
  bodySmall: const TextStyle().copyWith(fontSize: AppSizes.bodySmall, fontWeight: FontWeight.w500, color: AppColors.lightTitleColor.withOpacity(0.5)),

  labelLarge: const TextStyle().copyWith(fontSize: AppSizes.labelLarge, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor),
  labelMedium: const TextStyle().copyWith(fontSize: AppSizes.labelMedium, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor.withOpacity(0.5)),
  labelSmall: const TextStyle().copyWith(fontSize: AppSizes.labelSmall, fontWeight: FontWeight.normal, color: AppColors.lightTitleColor.withOpacity(0.5)),
);

static TextTheme dark = TextTheme(
  headlineLarge: const TextStyle().copyWith(fontSize: AppSizes.headlineLarge, fontWeight: FontWeight.bold, color: AppColors.darkTitleColor),
  headlineMedium: const TextStyle().copyWith(fontSize: AppSizes.headlineMedium, fontWeight: FontWeight.w600, color: AppColors.darkTitleColor),
  headlineSmall: const TextStyle().copyWith(fontSize: AppSizes.headlineSmall, fontWeight: FontWeight.w600, color: AppColors.darkTitleColor),

  titleLarge: const TextStyle().copyWith(fontSize: AppSizes.titleLarge, fontWeight: FontWeight.w600, color: AppColors.darkTitleColor),
  titleMedium: const TextStyle().copyWith(fontSize: AppSizes.titleMedium, fontWeight: FontWeight.w500, color: AppColors.darkTitleColor),
  titleSmall: const TextStyle().copyWith(fontSize: AppSizes.titleSmall, fontWeight: FontWeight.w400, color: AppColors.darkTitleColor),

  bodyLarge: const TextStyle().copyWith(fontSize: AppSizes.bodyLarge, fontWeight: FontWeight.w500, color: AppColors.darkTitleColor),
  bodyMedium: const TextStyle().copyWith(fontSize: AppSizes.bodyMedium, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor),
  bodySmall: const TextStyle().copyWith(fontSize: AppSizes.bodySmall, fontWeight: FontWeight.w500, color: AppColors.darkTitleColor.withOpacity(0.5)),

  labelLarge: const TextStyle().copyWith(fontSize: AppSizes.labelLarge, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor),
  labelMedium: const TextStyle().copyWith(fontSize: AppSizes.labelMedium, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor.withOpacity(0.5)),
  labelSmall: const TextStyle().copyWith(fontSize: AppSizes.labelSmall, fontWeight: FontWeight.normal, color: AppColors.darkTitleColor.withOpacity(0.5)),
);
}