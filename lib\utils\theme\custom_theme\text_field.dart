import 'package:flutter/material.dart';
import '../../constants/colors.dart';
import '../../constants/sizes.dart';

class CustomTextFieldTheme {
  CustomTextFieldTheme._();

  static InputDecorationTheme light = InputDecorationTheme(
    errorMaxLines: 3,
    fillColor: AppColors.lightFillColor,
    prefixIconColor: AppColors.lightIconsColor,
    suffixIconColor: AppColors.lightIconsColor,

    //constraints: const BoxConstraints.expand(height: 14.inputFieldHeight),
    labelStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      color: AppColors.lightHintTextColor,
    ),
    hintStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      color: AppColors.lightHintTextColor,
    ),
    errorStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      fontWeight: FontWeight.normal,
    ),
    floatingLabelStyle: const TextStyle().copyWith(
      color: AppColors.lightHintTextColor,
    ),
    border: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightBorderColor,
      ),
    ),
    disabledBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.greyColor),
    ),
    enabledBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.lightBorderColor,
      ),
    ),
    focusedBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.5,
        color: AppColors.lightBorderColor,
      ),
    ),
    errorBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.error),
    ),
    focusedErrorBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.warning),
    ),
  );

  static InputDecorationTheme dark = InputDecorationTheme(
    errorMaxLines: 3,
    fillColor: AppColors.darkFillColor,
    prefixIconColor: AppColors.darkIconsColor,
    suffixIconColor: AppColors.darkIconsColor,

    //constraints: const BoxConstraints.expand(height: 14.inputFieldHeight),
    labelStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      color: AppColors.darkHintTextColor,
    ),
    hintStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      color: AppColors.darkHintTextColor,
    ),
    errorStyle: const TextStyle().copyWith(
      fontSize: AppSizes.bodyMedium,
      fontWeight: FontWeight.normal,
    ),
    floatingLabelStyle: const TextStyle().copyWith(
      color: AppColors.darkHintTextColor,
    ),
    border: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.darkBorderColor,
      ),
    ),
    disabledBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.greyColor),
    ),
    enabledBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.0,
        color: AppColors.darkBorderColor,
      ),
    ),
    focusedBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(
        width: 1.5,
        color: AppColors.darkBorderColor,
      ),
    ),
    errorBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.error),
    ),
    focusedErrorBorder: const OutlineInputBorder().copyWith(
      borderRadius: BorderRadius.circular(AppSizes.inputFieldRadius),
      borderSide: const BorderSide(width: 1.0, color: AppColors.warning),
    ),
  );
}
