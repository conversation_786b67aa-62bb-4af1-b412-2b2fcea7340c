import 'package:flutter/material.dart';
import '../../core/models/user_model.dart';

/// Widget for displaying a device tile with expandable details
class DeviceTileWidget extends StatelessWidget {
  /// The device to display
  final Device device;

  /// Constructor
  const DeviceTileWidget({
    super.key,
    required this.device,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.devices,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        title: Text(device.name),
        subtitle: Text('${device.os} - ${device.appVersion}'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الحالة: ${device.status}'),
                const SizedBox(height: 8),
                const Text('الجلسات النشطة:'),
                ...device.sessions.map(
                  (session) => ListTile(
                    title: Text('IP: ${session.ipAddress}'),
                    subtitle: Text('الموقع: ${session.location}'),
                    trailing: Text('الحالة: ${session.status}'),
                  ),
                ),
                const SizedBox(height: 8),
                const Text('سجل النشاط:'),
                ...device.activityLogs.map(
                  (log) => ListTile(
                    title: Text(log.description),
                    subtitle: Text(log.timestamp),
                  ),
                ),
                const SizedBox(height: 8),
                const Text('سجل الأمان:'),
                ...device.securityLogs.map(
                  (log) => ListTile(
                    title: Text(log.activity),
                    subtitle: Text(log.timestamp),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
