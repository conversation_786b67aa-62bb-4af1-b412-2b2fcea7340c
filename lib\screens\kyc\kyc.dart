import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../../utils/helpers/helpers_functions.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class KYC extends StatelessWidget {
  const KYC({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return Scaffold(
      appBar: BuildAppBar(showBackArrow: true, title: Text('KYC Verification', style: Theme.of(context).textTheme.titleMedium,),),
      body: Padding(
        padding: const EdgeInsets.all(AppSizes.defaultSpace),
        child: ListView(
          children: <Widget>[
            const SectionCard(
              title: 'Personal Verification',
              status: 'LV.1 Verified',
              color: Colors.greenAccent,
              icon: Icons.person,
              children: <Widget>[
                VerificationLevel(
                  level: 'LV.1 Basic Verification',
                  limit: 'Transaction limit: 100,000 USD',
                  divider: true,
                  isVerified: true,
                ),
                VerificationLevel(
                  level: 'LV.2 Premium Verification',
                  limit: 'Transaction limit: 100,000,000 USD',
                  isVerified: false,
                ),
              ],
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              child: const Center(child: Text('LV.2 Premium Verification')),
              onPressed: () => _showVerificationDialog(context, 'LV.2 Premium Verification'),
            ),
            const SizedBox(height: 24),
            SectionCard(
              title: 'Enterprise Verification',
              status: 'Not Verified',
              color: dark ? AppColors.darkFillColor : AppColors.lightFillColor ,
              icon: Icons.business,
              children: const <Widget>[
                VerificationLevel(
                  level: 'Enterprise Verification',
                  limit: 'Transaction limit: 200,000,000 USD',
                  isVerified: false,
                ),
              ],
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              child: const Center(child: Text('Enterprise Verification')),
              onPressed: () => _showVerificationDialog(context, 'Enterprise Verification'),
            ),
            const SizedBox(height: 24),
            const RestrictedCountriesText(),
          ],
        ),
      ),
    );
  }

  void _showVerificationDialog(BuildContext context, String verificationType) {
    Get.dialog(
      AlertDialog(
        title: Text('Request $verificationType'),
        content: const Text('Are you sure you want to request this verification?'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // Handle verification request here
              // Perform input validation and API call securely
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}

class VerificationLevel extends StatelessWidget {
  const VerificationLevel({
    super.key,
    required this.level,
    required this.limit,
    this.divider = false,
    required this.isVerified,
  });

  final String level;
  final String limit;
  final bool divider;
  final bool isVerified;

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Column(
          children: <Widget>[
            Icon(
              isVerified ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isVerified ? Colors.greenAccent : dark ? AppColors.darkIconsColor : AppColors.lightIconsColor,
            ),
            const SizedBox(height: 5),
            if (divider) Column(
              children: <Widget>[
                Container(height: 3, width: 1.7, color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor),
                const SizedBox(height: 3),
                Container(height: 18, width: 1.7, color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor),
                const SizedBox(height: 3),
                Container(height: 3, width: 1.7, color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor),
              ],
            ),
            const SizedBox(height: 5),
          ],
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(level, style: const TextStyle(fontSize: 16)),
            Text(limit, style: TextStyle(fontSize: 11, color: dark ? AppColors.darkHintTextColor: AppColors.lightHintTextColor)),
          ],
        ),
      ],
    );
  }
}

class SectionCard extends StatelessWidget {
  const SectionCard({
    super.key,
    required this.icon,
    required this.title,
    required this.color,
    required this.status,
    required this.children,
  });

  final Color color;
  final String title;
  final String status;
  final IconData icon;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: const EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Icon(icon, size: 22),
                    const SizedBox(width: 8),
                    Text(title, style: Theme.of(context).textTheme.labelLarge),
                  ],
                ),
                Text(status, style: Theme.of(context).textTheme.labelLarge),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 0.5, right: 0.5),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomRight: Radius.circular(10),
                bottomLeft: Radius.circular(10),
              ),
              border: Border(
                bottom: BorderSide(color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor, width: 0.5),
                left: BorderSide(color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor, width: 0.5),
                right: BorderSide(color: dark ? AppColors.darkBorderColor : AppColors.lightBorderColor, width: 0.5),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RestrictedCountriesText extends StatelessWidget {
  const RestrictedCountriesText({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const Text(
      'The services provided by BitFuFu are not available to residents (hereinafter referred to as "restricted individuals") of the following countries, including the United States, Mainland China, Crimea, Cuba, Iran, Russia, North Korea, Syria, Singapore, and Venezuela, or any entity or individual subject to restrictions under applicable trade sanctions and export laws. The list above may not be exhaustive. Before using the services provided by BitFuFu, please ensure that you are not a "restricted individual".',
      style: TextStyle(fontSize: 12),
    );
  }
}