import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'package:intl/intl.dart';

import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../../common/widgets/custom_button/custom_button.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/helpers/helpers_functions.dart';
import '../../../core/providers/user_provider.dart';
import '../pay/payment.dart';

class TransferController extends GetxController {
  static TransferController get instance => Get.find();
  final UserController _userController = Get.find<UserController>();

  var amount = '0'.obs;
  var isExceeded = false.obs;
  var maxAmount = *********.00.obs;

  @override
  void onInit() {
    super.onInit();
    loadTransferLimit();
  }

  void loadTransferLimit() {
    try {
      final user = _userController.currentUser;
      if (user != null && user.user.account?.limits != null) {
        maxAmount.value = user.user.account!.limits.maxAmount;
        Get.log('Transfer limit loaded: ${maxAmount.value}');
      } else {
        Get.log('Using default transfer limit: ${maxAmount.value}');
      }
    } catch (e) {
      Get.log('Error loading transfer limit: $e', isError: true);
    }
  }

  // Handles input for numbers and dot
  void handleNumberInput(String number) {
    if (isExceeded.value) return;
    if (amount.value == '0' && number != '.') {
      amount.value = number;
    } else if (amount.value.contains('.') &&
        (number == '.' || amount.value.split('.')[1].length >= 2)) {
      return;
    } else {
      amount.value += number;
    }
    _checkExceeded();
  }

  // Clears the input
  void handleClearInput() {
    amount.value = '0';
    isExceeded.value = false;
  }

  // Handles backspace action
  void handleBackspace() {
    if (amount.value.length > 1) {
      amount.value = amount.value.substring(0, amount.value.length - 1);
    } else {
      amount.value = '0';
    }
    _checkExceeded();
  }

  // Formats the amount for display
  String formatAmount(String amount) {
    final formatter = NumberFormat.decimalPattern();
    if (amount.contains('.')) {
      var parts = amount.split('.');
      return '${formatter.format(int.parse(parts[0]))}.${parts[1]}';
    }
    return formatter.format(int.parse(amount));
  }

  // Checks if the amount exceeds the limit
  void _checkExceeded() {
    isExceeded.value =
        double.parse(amount.value.replaceAll(',', '')) > maxAmount.value;
  }
}

class Transfer extends StatelessWidget {
  const Transfer({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TransferController());

    final dark = HelperFunctions.isDarkMode(context);
    return Scaffold(
      appBar: BuildAppBar(
        leadingIcon: Icons.close,
        leadingOnPressed: () => Get.back(),
        action: [
          IconButton(icon: const Icon(Iconsax.maximize_21), onPressed: () {}),
          IconButton(icon: const Icon(Iconsax.info_circle), onPressed: () {}),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 30.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Center(
              child: Obx(
                () => Column(
                  children: [
                    Text(
                      '\$${controller.formatAmount(controller.amount.value)}',
                      style: TextStyle(
                        fontSize: 42,
                        fontWeight: FontWeight.bold,
                        color:
                            controller.isExceeded.value
                                ? AppColors.error
                                : dark
                                ? AppColors.darkTitleColor
                                : AppColors.lightTitleColor,
                      ),
                    ),
                    if (controller.isExceeded.value)
                      Obx(
                        () => Text(
                          'الحد الأقصى للتحويل هو ${NumberFormat.decimalPattern().format(controller.maxAmount.value)}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.error,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: double.infinity,
              child: GridView.builder(
                shrinkWrap: true,
                itemCount: 12,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: double.minPositive,
                  crossAxisSpacing: 50,
                ),
                itemBuilder: (context, index) {
                  if (index == 9) return const NumberButton(number: '.');
                  if (index == 10) return const NumberButton(number: '0');
                  if (index == 11) return const BackspaceButton();
                  return NumberButton(number: '${index + 1}');
                },
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CustomButton(
                    label: 'Send',
                    onPressed:
                        () => Get.to(
                          () => Payment(
                            amount: controller.formatAmount(
                              controller.amount.value,
                            ),
                          ),
                        ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(label: 'Request', onPressed: () {}),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class NumberButton extends StatelessWidget {
  const NumberButton({super.key, required this.number});
  final String number;

  @override
  Widget build(BuildContext context) {
    final controller = TransferController.instance;
    return ElevatedButton(
      onPressed: () => controller.handleNumberInput(number),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        shape: const CircleBorder(),
        side: BorderSide.none,
        elevation: 0,
      ),
      child: Text(
        number,
        style: const TextStyle(fontSize: 24, color: Colors.grey),
      ),
    );
  }
}

class BackspaceButton extends StatelessWidget {
  const BackspaceButton({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = TransferController.instance;
    return ElevatedButton(
      onPressed: controller.handleBackspace,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        shape: const CircleBorder(),
        side: BorderSide.none,
        elevation: 0,
      ),
      child: const Icon(Iconsax.arrow_left_2, color: Colors.grey, size: 24),
    );
  }
}
