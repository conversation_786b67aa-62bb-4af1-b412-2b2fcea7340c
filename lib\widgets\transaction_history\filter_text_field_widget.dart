import 'package:flutter/material.dart';

/// Widget for displaying a filter text field
class FilterTextFieldWidget extends StatelessWidget {
  /// The controller for the text field
  final TextEditingController controller;
  
  /// The label text
  final String label;
  
  /// The hint text
  final String hint;
  
  /// The icon to display
  final IconData icon;
  
  /// The keyboard type
  final TextInputType keyboardType;

  /// Constructor
  const FilterTextFieldWidget({
    super.key,
    required this.controller,
    required this.label,
    required this.hint,
    required this.icon,
    this.keyboardType = TextInputType.text,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        prefixIcon: Icon(icon),
      ),
      keyboardType: keyboardType,
    );
  }
}
