import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/providers/user_provider.dart';
import '../core/controllers/profile_controller.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileController>();
    final userController = Get.find<UserController>();

    return Obx(() {
      final user = userController.currentUser;

      if (user == null) {
        return const Scaffold(body: Center(child: Text('الرجاء تسجيل الدخول')));
      }

      return Scaffold(
        appBar: BuildAppBar(
          showBackArrow: true,
          title: const Text('Profile'),
          action: [
            IconButton(
              icon: const Icon(Iconsax.logout),
              onPressed: controller.logout,
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: CircleAvatar(
                  radius: 50,
                  child: Text(
                    user.user.fullName[0].toUpperCase(),
                    style: const TextStyle(fontSize: 32),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              _buildInfoSection('المعلومات الشخصية', [
                _buildInfoRow('الاسم', user.user.fullName),
                _buildInfoRow('البريد الإلكتروني', user.user.email),
                _buildInfoRow('رقم الهاتف', user.user.phone),
                _buildInfoRow('الجنس', user.user.gender),
                _buildInfoRow('الجنسية', user.user.nationality),
                _buildInfoRow('تاريخ الميلاد', user.user.dateOfBirth),
                _buildInfoRow(
                  'حالة التحقق',
                  user.user.kycStatus.toString().split('.').last,
                ),
                _buildInfoRow(
                  'تحقق البريد الإلكتروني',
                  user.user.verification.emailVerified
                      ? 'تم التحقق'
                      : 'لم يتم التحقق',
                ),
                _buildInfoRow(
                  'تحقق رقم الهاتف',
                  user.user.verification.phoneVerified
                      ? 'تم التحقق'
                      : 'لم يتم التحقق',
                ),
              ]),
              const SizedBox(height: 16),
              if (user.user.account != null)
                _buildInfoSection('معلومات الحساب', [
                  _buildInfoRow('رقم الحساب', user.user.account!.accountNumber),
                  _buildInfoRow(
                    'الرصيد',
                    '${user.user.account!.balance} ${user.user.account!.currency}',
                  ),
                  _buildInfoRow('نوع الحساب', user.user.account!.type),
                  _buildInfoRow('حالة الحساب', user.user.account!.status),
                ]),
              const SizedBox(height: 16),
              _buildInfoSection('العنوان', [
                _buildInfoRow('الشارع', user.user.address.street),
                _buildInfoRow('المدينة', user.user.address.city),
                _buildInfoRow('الولاية', user.user.address.state),
                _buildInfoRow('الرمز البريدي', user.user.address.zipCode),
                _buildInfoRow('البلد', user.user.address.country),
              ]),
              const SizedBox(height: 16),
              if (user.user.documents.isNotEmpty)
                _buildInfoSection('المستندات', [
                  for (final doc in user.user.documents)
                    Column(
                      children: [
                        _buildInfoRow('النوع', doc.type),
                        _buildInfoRow('الرقم', doc.number),
                        _buildInfoRow(
                          'تاريخ الإصدار',
                          _formatDate(doc.uploadedAt),
                        ),
                        _buildInfoRow(
                          'تاريخ الانتهاء',
                          _formatDate(doc.expiryDate),
                        ),
                        _buildInfoRow('الحالة', doc.status),
                        const Divider(),
                      ],
                    ),
                ]),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(children: children),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }
}
