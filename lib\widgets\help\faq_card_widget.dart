import 'package:flutter/material.dart';

/// Widget for displaying a FAQ card with expandable answer
class FAQCardWidget extends StatelessWidget {
  /// The question text
  final String question;
  
  /// The answer text
  final String answer;

  /// Constructor
  const FAQCardWidget({
    super.key,
    required this.question,
    required this.answer,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(answer),
          ),
        ],
      ),
    );
  }
}
