import 'package:flutter/material.dart';

/// Widget for displaying a label-value row in info cards
class InfoRowWidget extends StatelessWidget {
  /// The label text
  final String label;
  
  /// The value text
  final String value;

  /// Constructor
  const InfoRowWidget({
    super.key,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }
}
