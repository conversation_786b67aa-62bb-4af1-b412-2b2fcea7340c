// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:owallet/core/controllers/settings_controller.dart';
import 'package:owallet/main.dart';

void main() {
  testWidgets('App initialization test', (WidgetTester tester) async {
    // Create a mock SettingsController for testing
    final mockSettingsController = SettingsController();

    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp(settingsController: mockSettingsController));

    // Verify that the app initializes without errors
    expect(find.byType(GetMaterialApp), findsOneWidget);
  });
}
