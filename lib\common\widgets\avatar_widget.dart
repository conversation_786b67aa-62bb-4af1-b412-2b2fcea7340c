import 'package:flutter/material.dart';
import 'package:owallet/utils/helpers/helpers_functions.dart';
import '../../core/enums/app_enums.dart';
import '../../core/utils/transaction_utils.dart';
import '../../utils/constants/colors.dart';

class AvatarWidget extends StatelessWidget {
  final TransactionType? transactionType;
  final double size;
  final double? iconSize;
  final Color? background;
  final String? letter;
  final IconData? icon;

  const AvatarWidget({
    super.key,
    this.transactionType,
    this.size = 46.0,
    this.iconSize,
    this.background,
    this.letter,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = HelperFunctions.isDarkMode(context);

    final Widget child =
        icon != null
            ? Icon(
              icon,
              size: iconSize,
              color:
                  isDark ? AppColors.darkIconsColor : AppColors.lightIconsColor,
            )
            : transactionType != null
            ? Icon(
              TransactionUtils.getTransactionIcon(transactionType!),
              color:
                  isDark ? AppColors.darkIconsColor : AppColors.lightIconsColor,
              size: iconSize,
            )
            : Text(
              (letter?.isNotEmpty ?? false) ? letter![0].toUpperCase() : '',
              style: Theme.of(context).textTheme.bodyLarge,
            );

    return CircleAvatar(
      radius: size / 2,
      backgroundColor:
          isDark ? AppColors.darkFillColor : AppColors.lightFillColor,
      child: child,
    );
  }
}
