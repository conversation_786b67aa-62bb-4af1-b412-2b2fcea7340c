import 'package:flutter/material.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';
import '../../utils/helpers/helpers_functions.dart';

/// Widget for displaying a status tile with a title, status, and tap action
class StatusTileWidget extends StatelessWidget {
  /// The title of the status
  final String title;

  /// Optional subtitle to display below the title
  final String? subtitle;

  /// The status text to display (optional)
  final String? status;

  /// Callback when the tile is tapped
  final VoidCallback? onTap;

  /// Optional icon to display in the leading container
  final IconData? leadingIcon;

  /// Optional icon to display in the trailing container
  final IconData? trailingIcon;

  /// Constructor
  const StatusTileWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.status,
    this.onTap,
    this.leadingIcon,
    this.trailingIcon,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return ListTile(
      leading:
          leadingIcon != null
              ? Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color:
                      dark ? AppColors.darkFillColor : AppColors.lightFillColor,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(leadingIcon),
              )
              : null,
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (status != null) ...[
            Text(status!, style: Theme.of(context).textTheme.bodySmall),
            const SizedBox(width: AppSizes.sm),
          ],
          if (trailingIcon != null) Icon(trailingIcon!, size: AppSizes.iconSm),
        ],
      ),
      onTap: onTap,
    );
  }
}
