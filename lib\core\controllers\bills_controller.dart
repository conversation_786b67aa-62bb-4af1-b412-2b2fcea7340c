import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';

/// Model class for bill
class Bill {
  final String id;
  final String type;
  final String provider;
  final String accountNumber;
  final double amount;
  final String dueDate;
  final String status;
  final String? logoUrl;

  Bill({
    required this.id,
    required this.type,
    required this.provider,
    required this.accountNumber,
    required this.amount,
    required this.dueDate,
    required this.status,
    this.logoUrl,
  });
}

/// Controller responsible for managing bills
class BillsController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final RxBool isLoading = false.obs;
  final RxList<Bill> bills = <Bill>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadBills();
  }

  /// Load bills (mock data for now)
  void loadBills() {
    isLoading.value = true;

    // Mock data - in a real app, this would come from an API
    bills.value = [
      Bill(
        id: '1',
        type: 'كهرباء',
        provider: 'شركة الكهرباء الوطنية',
        accountNumber: '*********',
        amount: 75.50,
        dueDate: '2023-12-15',
        status: 'غير مدفوعة',
        logoUrl: 'assets/images/electricity.png',
      ),
      Bill(
        id: '2',
        type: 'مياه',
        provider: 'شركة المياه',
        accountNumber: '*********',
        amount: 45.20,
        dueDate: '2023-12-20',
        status: 'غير مدفوعة',
        logoUrl: 'assets/images/water.png',
      ),
      Bill(
        id: '3',
        type: 'انترنت',
        provider: 'شركة الاتصالات',
        accountNumber: '*********',
        amount: 120.00,
        dueDate: '2023-12-10',
        status: 'مدفوعة',
        logoUrl: 'assets/images/internet.png',
      ),
      Bill(
        id: '4',
        type: 'هاتف',
        provider: 'شركة الاتصالات',
        accountNumber: '*********',
        amount: 35.75,
        dueDate: '2023-12-25',
        status: 'غير مدفوعة',
        logoUrl: 'assets/images/phone.png',
      ),
    ];

    isLoading.value = false;
  }

  /// Get icon for bill type
  IconData getBillTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'كهرباء':
        return Icons.electric_bolt;
      case 'مياه':
        return Icons.water_drop;
      case 'انترنت':
        return Icons.wifi;
      case 'هاتف':
        return Icons.phone;
      default:
        return Icons.receipt_long;
    }
  }

  /// Get color for bill status
  Color getBillStatusColor(String status) {
    switch (status) {
      case 'مدفوعة':
        return Colors.green;
      case 'غير مدفوعة':
        return Colors.red;
      case 'متأخرة':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// Pay a bill
  Future<void> payBill(Bill bill) async {
    try {
      isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Update bill status
      final index = bills.indexWhere((b) => b.id == bill.id);
      if (index != -1) {
        final updatedBill = Bill(
          id: bill.id,
          type: bill.type,
          provider: bill.provider,
          accountNumber: bill.accountNumber,
          amount: bill.amount,
          dueDate: bill.dueDate,
          status: 'مدفوعة',
          logoUrl: bill.logoUrl,
        );

        bills[index] = updatedBill;
        bills.refresh();
      }

      Get.snackbar(
        'تم الدفع',
        'تم دفع الفاتورة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء دفع الفاتورة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Add a new bill (placeholder for future implementation)
  Future<void> addBill() async {
    Get.snackbar(
      'ملاحظة',
      'ستتم إضافة هذه الميزة قريبًا',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }
}
