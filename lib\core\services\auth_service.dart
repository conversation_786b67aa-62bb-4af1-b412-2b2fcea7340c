import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';

class AuthService {
  static List<UserModel>? _users;

  static Future<List<UserModel>> getUsers() async {
    if (_users != null) return _users!;

    try {
      Get.log('Loading users from data.json...', isError: false);
      final String jsonString = await rootBundle.loadString(
        'assets/data/data.json',
      );
      Get.log(
        'JSON string loaded successfully, length: ${jsonString.length}',
        isError: false,
      );

      if (jsonString.isEmpty) {
        Get.log('Error: JSON string is empty', isError: true);
        return [];
      }

      final List<dynamic> jsonData = json.decode(jsonString);
      Get.log(
        'JSON decoded successfully, found ${jsonData.length} users',
        isError: false,
      );

      if (jsonData.isEmpty) {
        Get.log('Error: No users found in JSON data', isError: true);
        return [];
      }

      _users =
          jsonData.map((userJson) {
            try {
              if (userJson == null) {
                Get.log('Error: user data is null', isError: true);
                throw Exception('User data is null');
              }

              // Ensure we have a 'user' field
              if (userJson['user'] == null) {
                Get.log('Error: user data is missing', isError: true);
                throw Exception('User data is missing');
              }

              // Ensure we have required fields
              final userData = userJson['user'];
              if (userData['id'] == null ||
                  userData['fullName'] == null ||
                  userData['email'] == null) {
                Get.log('Error: Required fields are missing', isError: true);
                throw Exception('Required fields are missing');
              }

              Get.log('Parsing user data: ${userJson['user']}', isError: false);
              return UserModel.fromJson(userJson);
            } catch (e) {
              Get.log('Error parsing user: $e', isError: true);
              Get.log(
                'Problematic user data: ${json.encode(userJson)}',
                isError: true,
              );
              rethrow;
            }
          }).toList();

      Get.log('Successfully parsed ${_users!.length} users', isError: false);
      return _users!;
    } catch (e) {
      Get.log('Error loading users: $e', isError: true);
      Get.log('Error details: ${e.toString()}', isError: true);
      Get.log('Stack trace: ${StackTrace.current}', isError: true);
      return [];
    }
  }

  static Future<UserModel?> login(String email) async {
    try {
      Get.log('Attempting login for email: $email', isError: false);
      final users = await getUsers();
      if (users.isEmpty) {
        Get.log('No users found in the database', isError: true);
        return null;
      }

      final user = users.firstWhereOrNull(
        (u) => u.user.email.toLowerCase() == email.toLowerCase(),
      );

      if (user != null) {
        Get.log(
          'Login successful for user: ${user.user.fullName}',
          isError: false,
        );
        return user;
      }

      Get.log('User not found', isError: true);
      return null;
    } catch (e) {
      Get.log('Error during login: $e', isError: true);
      Get.log('Error details: ${e.toString()}', isError: true);
      return null;
    }
  }

  static Future<bool> validateEmail(String email) async {
    try {
      final users = await getUsers();
      return users.any(
        (user) => user.user.email.toLowerCase() == email.toLowerCase(),
      );
    } catch (e) {
      Get.log('Error validating email: $e', isError: true);
      Get.log('Error details: ${e.toString()}', isError: true);
      return false;
    }
  }

  static Future<bool> validatePhone(String phone) async {
    try {
      final users = await getUsers();
      return users.any((user) => user.user.phone == phone);
    } catch (e) {
      Get.log('Error validating phone: $e', isError: true);
      Get.log('Error details: ${e.toString()}', isError: true);
      return false;
    }
  }

  static Future<UserModel?> getUserById(String id) async {
    try {
      final users = await getUsers();
      return users.firstWhereOrNull((user) => user.user.id == id);
    } catch (e) {
      Get.log('Error getting user by id: $e', isError: true);
      Get.log('Error details: ${e.toString()}', isError: true);
      return null;
    }
  }
}
