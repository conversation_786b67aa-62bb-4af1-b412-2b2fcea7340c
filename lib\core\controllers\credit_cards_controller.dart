import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';

/// Controller responsible for managing credit cards
class CreditCardsController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final RxBool isLoading = false.obs;
  final RxList<CreditCard> creditCards = <CreditCard>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadCreditCards();
  }

  /// Load credit cards from user data
  void loadCreditCards() {
    final user = userController.currentUser;
    if (user != null && user.user.account != null) {
      creditCards.value = user.user.account!.creditCards;
    }
  }

  /// Get card type icon
  IconData getCardTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'visa':
        return Icons.credit_card;
      case 'mastercard':
        return Icons.credit_card;
      case 'amex':
        return Icons.credit_card;
      default:
        return Icons.credit_card;
    }
  }

  /// Get card status color
  Color getCardStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.grey;
      case 'blocked':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Format card number with asterisks
  String formatCardNumber(String maskedNumber) {
    if (maskedNumber.length < 4) return maskedNumber;
    
    // Assuming the masked number is in format: **** **** **** 1234
    return maskedNumber;
  }

  /// Add a new credit card (placeholder for future implementation)
  Future<void> addCreditCard() async {
    // This would be implemented to add a new card
    Get.snackbar(
      'ملاحظة',
      'ستتم إضافة هذه الميزة قريبًا',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// Remove a credit card (placeholder for future implementation)
  Future<void> removeCreditCard(String cardId) async {
    try {
      isLoading.value = true;
      
      // This would be implemented to remove a card
      // For now, just show a message
      Get.snackbar(
        'ملاحظة',
        'ستتم إضافة هذه الميزة قريبًا',
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
