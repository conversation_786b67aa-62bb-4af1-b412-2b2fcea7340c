import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';

class ElevatedCustomButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;

  const ElevatedCustomButton({
    super.key,
    required this.label,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {

    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: AppSizes.md +2),
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100),
        ),
        backgroundColor: Colors.transparent,
      ),
      onPressed: onPressed,
      child: Center(child: Text(label,)),
    );
  }
}