import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller responsible for managing withdrawal of funds
class WithdrawFundsController extends GetxController {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _selectedBank = ''.obs;
  final _isProcessing = false.obs;

  final List<Map<String, dynamic>> _bankAccounts = [
    {
      'id': '1',
      'bankName': 'Chase Bank',
      'accountNumber': '**** **** **** 1234',
      'accountType': 'Checking',
    },
    {
      'id': '2',
      'bankName': 'Bank of America',
      'accountNumber': '**** **** **** 5678',
      'accountType': 'Savings',
    },
  ];

  @override
  void onClose() {
    _amountController.dispose();
    super.onClose();
  }

  /// Process the withdrawal of funds
  void withdrawFunds() {
    if (_formKey.currentState!.validate()) {
      if (_selectedBank.value.isEmpty) {
        Get.snackbar(
          'Error',
          'Please select a bank account',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      _isProcessing.value = true;

      // Simulate processing
      Future.delayed(const Duration(seconds: 2), () {
        _isProcessing.value = false;

        // Navigate to transaction details screen
        Get.toNamed(
          '/transaction-details',
          arguments: {
            'recipient': _bankAccounts.firstWhere(
                (bank) => bank['id'] == _selectedBank.value)['bankName'],
            'amount': double.parse(_amountController.text),
            'transactionType': 'Withdrawn',
            'transactionDate': DateTime.now(),
            'note': 'Withdrawal to bank account',
            'status': 'Processing',
          },
        );
      });
    }
  }

  /// Add a new bank account
  void addBankAccount() {
    // This would be implemented when the add bank account screen is created
    Get.snackbar(
      'Coming Soon',
      'Add bank account feature will be available soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Getters
  GlobalKey<FormState> get formKey => _formKey;
  TextEditingController get amountController => _amountController;
  RxString get selectedBank => _selectedBank;
  RxBool get isProcessing => _isProcessing;
  List<Map<String, dynamic>> get bankAccounts => _bankAccounts;
}
