import 'package:flutter/material.dart';
import '../../constants/colors.dart';

class CustomCheckboxTheme {
  /// --To Avoid Creating Instances
  CustomCheckboxTheme._();

  static CheckboxThemeData light = CheckboxThemeData(
    side: const BorderSide(color: AppColors.lightBorderColor,width: 1.2),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
    checkColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.light;
        } else {
          return AppColors.lightIconsColor;
        }
      },
    ),
    fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        } else {
          return Colors.transparent;
        }
      },
    ),
  );

  static CheckboxThemeData dark = CheckboxThemeData(
    side: const BorderSide(color: AppColors.darkBorderColor,width: 1.1),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
    checkColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.light;
      } else {
        return AppColors.lightIconsColor;
      }
    },
    ),
    fillColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      } else {
        return Colors.transparent;
      }
    },
    ),
  );

}
