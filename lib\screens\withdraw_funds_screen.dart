import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/withdraw_funds_controller.dart';

class WithdrawFundsScreen extends StatelessWidget {
  const WithdrawFundsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WithdrawFundsController>();

    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('Withdraw Funds'),
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Amount Input
              const Text(
                'Withdrawal Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: controller.amountController,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                decoration: InputDecoration(
                  hintText: 'Enter amount',
                  prefixIcon: const Icon(Icons.attach_money),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid amount';
                  }
                  if (double.parse(value) <= 0) {
                    return 'Amount must be greater than zero';
                  }
                  if (double.parse(value) > 1234.56) {
                    return 'Amount exceeds available balance';
                  }
                  return null;
                },
              ),

              // Bank Account Selection
              const Text(
                'Select Bank Account',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Obx(
                () => Column(
                  children:
                      controller.bankAccounts.map((bank) {
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: RadioListTile<String>(
                            title: Text(bank['bankName']),
                            subtitle: Text(
                              '${bank['accountType']} - ${bank['accountNumber']}',
                            ),
                            value: bank['id'],
                            groupValue: controller.selectedBank.value,
                            onChanged: (value) {
                              controller.selectedBank.value = value!;
                            },
                          ),
                        );
                      }).toList(),
                ),
              ),
              const SizedBox(height: 16),
              OutlinedButton.icon(
                onPressed: controller.addBankAccount,
                icon: const Icon(Icons.add),
                label: const Text('Add New Bank Account'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Withdraw Button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed:
                        controller.isProcessing.value
                            ? null
                            : controller.withdrawFunds,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        controller.isProcessing.value
                            ? const CircularProgressIndicator()
                            : const Text(
                              'Withdraw Funds',
                              style: TextStyle(fontSize: 16),
                            ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Withdrawal Information
              const Text(
                'Withdrawal Information',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      _buildInfoRow(
                        'Processing Time',
                        '1-2 business days',
                        Icons.timer,
                      ),
                      _buildInfoRow('Fees', 'No fees', Icons.info_outline),
                      _buildInfoRow(
                        'Limits',
                        'Min: \$5, Max: \$10,000',
                        Icons.lock_outline,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          Text(value),
        ],
      ),
    );
  }
}
