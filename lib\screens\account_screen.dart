import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../common/widgets/appbar/custom_appbar.dart';
import '../widgets/account/status_tile_widget.dart';

class AccountScreen extends StatelessWidget {
  const AccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: Text('Account'),
        action: [
          IconButton(
            onPressed: () => Get.toNamed('/help-support'),
            icon: const Icon(Iconsax.message),
          ),
          IconButton(
            onPressed: () => Get.toNamed('/settings'),
            icon: const Icon(Iconsax.setting_25),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSettingTile(
              'Profile',
              leadingIcon: Icons.person,
              () => Get.toNamed('/profile'),
            ),
            _buildSettingTile(
              'Credit Cards',
              leadingIcon: Icons.credit_card,
              () => Get.toNamed('/credit-cards'),
            ),
            _buildSettingTile(
              'Contacts',
              leadingIcon: Icons.person_add,
              () => Get.toNamed('/contacts'),
            ),
            _buildSettingTile(
              'History',
              leadingIcon: Icons.history,
              () => Get.toNamed('/transaction-history'),
            ),
            _buildSettingTile(
              'Notifications',
              leadingIcon: Icons.notifications,
              () => Get.toNamed('/notifications'),
            ),
            _buildSettingTile('About', leadingIcon: Icons.info, () {
              Get.toNamed('/about');
            }),
          ],
        ),
      ),
    );
  }
}

StatusTileWidget _buildSettingTile(
  String title,
  VoidCallback onTap, {

  IconData? leadingIcon,
}) {
  return StatusTileWidget(
    title: title,
    onTap: onTap,
    leadingIcon: leadingIcon,
    trailingIcon: Icons.arrow_forward_ios,
  );
}
