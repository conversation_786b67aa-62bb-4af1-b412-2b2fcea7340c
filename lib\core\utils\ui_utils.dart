import 'package:flutter/material.dart';
import '../../utils/constants/sizes.dart';

/// Utility class for UI-related operations
class UiUtils {
  /// Create a standard vertical spacing
  static Widget verticalSpace(double height) {
    return SizedBox(height: height);
  }

  /// Create a standard horizontal spacing
  static Widget horizontalSpace(double width) {
    return SizedBox(width: width);
  }

  /// Create a standard divider
  static Widget divider({
    double height = AppSizes.dividerHeight,
    Color? color,
  }) {
    return Divider(height: height, color: color);
  }

  /// Create a standard vertical divider
  static Widget verticalDivider({
    double width = AppSizes.dividerHeight,
    Color? color,
  }) {
    return VerticalDivider(width: width, color: color);
  }

  /// Create a standard card
  static Widget card({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? color,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return Card(
      margin: margin ?? EdgeInsets.all(AppSizes.sm),
      elevation: elevation ?? AppSizes.cardElevation,
      color: color,
      shape:
          shape ??
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
          ),
      child: Padding(
        padding: padding ?? EdgeInsets.all(AppSizes.md),
        child: child,
      ),
    );
  }

  /// Create a standard section header
  static Widget sectionHeader(String title, {TextStyle? style}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppSizes.sm),
      child: Text(
        title,
        style:
            style ??
            TextStyle(
              fontSize: AppSizes.headlineSmall,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  /// Create a standard section with header and content
  static Widget section({
    required String title,
    required Widget content,
    TextStyle? titleStyle,
    EdgeInsetsGeometry? padding,
  }) {
    return Padding(
      padding: padding ?? EdgeInsets.symmetric(vertical: AppSizes.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionHeader(title, style: titleStyle),
          verticalSpace(AppSizes.sm),
          content,
        ],
      ),
    );
  }

  /// Create a standard label-value row
  static Widget labelValueRow({
    required String label,
    required String value,
    TextStyle? labelStyle,
    TextStyle? valueStyle,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppSizes.xs),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style:
                labelStyle ??
                TextStyle(color: Colors.grey, fontSize: AppSizes.bodyMedium),
          ),
          Text(
            value,
            style:
                valueStyle ??
                TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: AppSizes.bodyMedium,
                ),
          ),
        ],
      ),
    );
  }

  /// Create a standard icon with label
  static Widget iconWithLabel({
    required IconData icon,
    required String label,
    Color? iconColor,
    TextStyle? labelStyle,
    double iconSize = AppSizes.iconMd,
    VoidCallback? onTap,
  }) {
    final widget = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: iconColor, size: iconSize),
        verticalSpace(AppSizes.xs),
        Text(
          label,
          style: labelStyle ?? TextStyle(fontSize: AppSizes.bodySmall),
        ),
      ],
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.sm),
        child: Padding(padding: EdgeInsets.all(AppSizes.sm), child: widget),
      );
    }

    return widget;
  }

  /// Create a standard action button
  static Widget actionButton({
    required String label,
    required VoidCallback onPressed,
    IconData? icon,
    Color? color,
    Color? textColor,
    double? width,
    double height = AppSizes.appBarHeight,
  }) {
    final button = ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: textColor,
        minimumSize: Size(width ?? double.infinity, height),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.sm),
        ),
      ),
      child:
          icon != null
              ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon),
                  horizontalSpace(AppSizes.sm),
                  Text(label),
                ],
              )
              : Text(label),
    );

    return width != null
        ? SizedBox(width: width, height: height, child: button)
        : button;
  }
}
