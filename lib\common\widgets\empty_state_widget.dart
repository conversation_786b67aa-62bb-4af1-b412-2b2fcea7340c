import 'package:flutter/material.dart';

/// A shared widget for displaying an empty state
class EmptyStateWidget extends StatelessWidget {
  /// The icon to display
  final IconData icon;
  
  /// The title text to display
  final String title;
  
  /// The subtitle text to display (optional)
  final String? subtitle;
  
  /// The icon size
  final double iconSize;
  
  /// The icon color
  final Color? iconColor;

  /// Constructor
  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.iconSize = 64,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: iconColor ?? Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              subtitle!,
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
