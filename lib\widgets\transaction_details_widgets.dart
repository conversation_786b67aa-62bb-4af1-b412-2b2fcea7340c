import 'package:flutter/material.dart';
import '../common/widgets/app_card_widget.dart';
import '../common/widgets/detail_row_widget.dart';
import '../core/controllers/transaction_details_controller.dart';


/// Widget for building the status card
Widget buildStatusCard(TransactionDetailsController controller) {
  return AppCardWidget(
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: controller.getStatusColor().withAlpha(30),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check_circle,
            color: controller.getStatusColor(),
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة المعاملة',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            Text(
              controller.status,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: controller.getStatusColor(),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

/// Widget for building the amount card
Widget buildAmountCard(TransactionDetailsController controller) {
  final isReceive = controller.transactionType.toLowerCase() == 'receive';
  final color = isReceive ? Colors.green : Colors.red;

  return AppCardWidget(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('المبلغ', style: TextStyle(fontSize: 14, color: Colors.grey)),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(controller.getTransactionTypeIcon(), color: color, size: 24),
            const SizedBox(width: 8),
            Text(
              controller.getFormattedAmount(),
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          isReceive ? 'تم استلام المبلغ' : 'تم إرسال المبلغ',
          style: TextStyle(fontSize: 14, color: color),
        ),
      ],
    ),
  );
}

/// Widget for building the details card
Widget buildDetailsCard(TransactionDetailsController controller) {
  return AppCardWidget(
    // title: 'تفاصيل المعاملة',
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (controller.fromAccount != null)
          DetailRowWidget(label: 'From', value: controller.fromAccount ?? ''),
        if (controller.recipient != null)
          DetailRowWidget(label: 'To', value: controller.recipient ?? ''),
        DetailRowWidget(label: 'Date', value: controller.getFormattedDate()),
        DetailRowWidget(label: 'Date', value: controller.status),
        DetailRowWidget(label: 'Trx ID', value: controller.reference),

        if (controller.fee != null)
          DetailRowWidget(
            label: 'Fee',
            value: controller.getFormattedFee() ?? '',
          ),
      ],
    ),
  );
}

/// Widget for building the note card
Widget buildNoteCard(TransactionDetailsController controller) {
  if (controller.note.isEmpty) return const SizedBox.shrink();

  return AppCardWidget(title: 'Note', child: Text(controller.note));
}






