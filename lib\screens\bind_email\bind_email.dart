import 'package:flutter/material.dart';

import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../../utils/constants/colors.dart';
import '../../common/widgets/text form field/costume_text_field.dart';


class BindEmail extends StatefulWidget {
  const BindEmail({super.key});

  @override
  BindEmailState createState() => BindEmailState();
}

class BindEmailState extends State<BindEmail> {
  final TextEditingController _currentEmailController = TextEditingController();
  final TextEditingController _newEmailController = TextEditingController();
  final TextEditingController _currentEmailVerificationCodeController =
      TextEditingController();
  final TextEditingController _newEmailVerificationCodeController =
      TextEditingController();

  bool isCurrentEmailVerified = false;
  bool isNewEmailVerified = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BuildAppBar(
        showBackArrow: true,
        title: Text('Bind your email'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: <Widget>[
            Text(
              '*After changing the email address, you will be prevented from financial transactions for 24 hours.',
              style: Theme.of(context).textTheme.bodyMedium?.apply(color: AppColors.error),
            ),
            const SizedBox(height: 20),
            const Text('Current email', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            CostumeTextField(
              borderRadius: 12.0,
              controller: _currentEmailController,
              keyboardType: TextInputType.emailAddress,
              hintText: 'm****@gmail.com',
              enabled: false,
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              borderRadius: 12.0,
              keyboardType: TextInputType.emailAddress,
              controller: _currentEmailVerificationCodeController,
              hintText: 'Enter the verification code',
              suffixIcon: TextButton(
                onPressed: () {},
                child: const Text('Send'),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'New email',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            CostumeTextField(
              borderRadius: 12.0,
              controller: _newEmailController,
              keyboardType: TextInputType.emailAddress,
              hintText: 'Enter the email address',
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              borderRadius: 12.0,
              controller: _newEmailVerificationCodeController,
              hintText: 'Enter the verification code',
              suffixIcon: TextButton(
                onPressed: () {},
                child: const Text('Send'),
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed:(){},
              child: const Center(child: Text('Confirm')),
            ),
          ],
        ),
      ),
    );
  }
}


