import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';

class UserController extends GetxController {
  // Private variables
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final RxList<UserModel> _users = <UserModel>[].obs;
  final RxBool _isLoading = false.obs;
  bool _isDataLoaded = false;

  // Streams for activity monitoring
  final _transactionsStreamController =
      StreamController<List<Transaction>>.broadcast();
  final _accountStreamController = StreamController<Account?>.broadcast();
  final _securityStreamController = StreamController<Security>.broadcast();

  // Public getters
  UserModel? get currentUser => _currentUser.value;
  Rx<UserModel?> get currentUserRx => _currentUser;
  List<UserModel> get users => _users;
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _currentUser.value != null;

  // Helper getters
  String get name => currentUser?.user.fullName ?? '';
  String get email => currentUser?.user.email ?? '';
  String get phone => currentUser?.user.phone ?? '';
  double get balance => currentUser?.user.account?.balance ?? 0.0;
  List<Transaction> get transactions =>
      currentUser?.user.account?.transactions ?? [];

  // Stream getters
  Stream<List<Transaction>> get transactionsStream =>
      _transactionsStreamController.stream;
  Stream<Account?> get accountStream => _accountStreamController.stream;
  Stream<Security> get securityStream => _securityStreamController.stream;

  @override
  void onInit() {
    super.onInit();
    loadUsers();
  }

  Future<void> loadUsers() async {
    if (_isDataLoaded) return;

    try {
      _isLoading.value = true;

      // Load data from JSON file
      final String jsonString = await rootBundle.loadString(
        'assets/data/data.json',
      );

      // Parse JSON data
      final dynamic jsonData = json.decode(jsonString);

      // Handle both array format and object format with 'users' key
      List<dynamic> jsonList = [];

      if (jsonData is List) {
        // Original format: array of user objects
        jsonList = jsonData;
      } else if (jsonData is Map<String, dynamic> &&
          jsonData.containsKey('users')) {
        // New format: object with 'users' key
        jsonList = jsonData['users'] as List<dynamic>;
      } else {
        throw Exception('Invalid data format in data file');
      }

      if (jsonList.isEmpty) {
        throw Exception('No users found in data file');
      }

      _users.clear();
      for (final json in jsonList) {
        try {
          final user = UserModel.fromJson(json);
          _users.add(user);
        } catch (e) {
          Get.log('Error parsing user: $e', isError: true);
          Get.log('User JSON: ${jsonEncode(json)}', isError: true);
        }
      }

      if (_users.isEmpty) {
        throw Exception('No valid users found in data file');
      }

      _isDataLoaded = true;

      // Log successful data loading
      Get.log('Successfully loaded ${_users.length} users from data file');
    } catch (e) {
      Get.log('Error loading users: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحميل البيانات: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> login(String email, String password) async {
    try {
      _isLoading.value = true;
      final user = _users.firstWhereOrNull((u) => u.user.email == email);

      if (user != null) {
        _currentUser.value = user;

        // Emit account and security data to streams for notification service
        if (user.user.account != null) {
          _accountStreamController.add(user.user.account);
        }
        _securityStreamController.add(user.user.security);

        // Emit transactions to stream for notification service
        if (user.user.account != null &&
            user.user.account!.transactions.isNotEmpty) {
          _transactionsStreamController.add(user.user.account!.transactions);
        }

        return true;
      }
      return false;
    } catch (e) {
      Get.log('Error in login: $e', isError: true);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  void logout() {
    _currentUser.value = null;
  }

  Future<Map<String, dynamic>> transferMoney(
    String toAccount,
    double amount,
    String description,
  ) async {
    try {
      final sender = currentUser;
      if (sender == null) throw Exception('Not logged in');
      if (sender.user.account == null) throw Exception('No account found');

      // Check if sender has sufficient balance
      if (amount > (sender.user.account?.balance ?? 0)) {
        return {'success': false, 'message': 'رصيد غير كافٍ'};
      }

      // Create transaction
      final transaction = Transaction(
        id: 'TXN${DateTime.now().millisecondsSinceEpoch}',
        type: 'sent',
        amount: amount,
        currency: sender.user.account?.currency ?? 'USD',
        description: description,
        status: 'completed',
        timestamp: DateTime.now(),
        fromAccount: sender.user.account?.accountNumber ?? '',
        toAccount: toAccount,
        reference: 'REF${DateTime.now().millisecondsSinceEpoch}',
        fee: 0.0,
      );

      // Update sender's balance and transactions
      final updatedSender = UserModel(
        user: User(
          id: sender.user.id,
          fullName: sender.user.fullName,
          email: sender.user.email,
          phone: sender.user.phone,
          gender: sender.user.gender,
          nationality: sender.user.nationality,
          dateOfBirth: sender.user.dateOfBirth,
          kycStatus: sender.user.kycStatus,
          verification: sender.user.verification,
          preferences: sender.user.preferences,
          notifications: sender.user.notifications,
          account: Account(
            accountNumber: sender.user.account?.accountNumber ?? '',
            type: sender.user.account?.type ?? '',
            currency: sender.user.account?.currency ?? 'USD',
            balance: (sender.user.account?.balance ?? 0) - amount,
            status: sender.user.account?.status ?? 'active',
            createdAt: sender.user.account?.createdAt ?? DateTime.now(),
            lastLogin: sender.user.account?.lastLogin ?? DateTime.now(),
            limits:
                sender.user.account?.limits ??
                AccountLimits(minAmount: 0, maxAmount: 0, dailyTransfer: 0, dailyWithdrawal: 0),
            transactions: [
              ...(sender.user.account?.transactions ?? []),
              transaction,
            ],
            creditCards: sender.user.account?.creditCards ?? [],
          ),
          address: sender.user.address,
          documents: sender.user.documents,
          contacts: sender.user.contacts,
          security: sender.user.security,
        ),
      );

      _currentUser.value = updatedSender;

      // Emit transaction to stream for notification service
      _transactionsStreamController.add([transaction, ...transactions]);

      return {'success': true, 'transaction': transaction.toJson()};
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    }
  }

  Future<void> updateContacts(List<Contact> contacts) async {
    try {
      if (currentUser == null) throw Exception('Not logged in');

      final updatedUser = UserModel(
        user: User(
          id: currentUser!.user.id,
          fullName: currentUser!.user.fullName,
          email: currentUser!.user.email,
          phone: currentUser!.user.phone,
          gender: currentUser!.user.gender,
          nationality: currentUser!.user.nationality,
          dateOfBirth: currentUser!.user.dateOfBirth,
          kycStatus: currentUser!.user.kycStatus,
          verification: currentUser!.user.verification,
          preferences: currentUser!.user.preferences,
          notifications: currentUser!.user.notifications,
          account: currentUser!.user.account,
          address: currentUser!.user.address,
          documents: currentUser!.user.documents,
          contacts: contacts,
          security: currentUser!.user.security,
        ),
      );

      // Update current user
      _currentUser.value = updatedUser;

      // Update users list
      final userIndex = _users.indexWhere(
        (u) => u.user.id == currentUser!.user.id,
      );
      if (userIndex != -1) {
        _users[userIndex] = updatedUser;
      }
    } catch (e) {
      Get.log('Error updating contacts: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحديث جهات الاتصال',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  UserModel? getUserById(String id) {
    if (id.isEmpty) return null;
    return _users.firstWhereOrNull((user) => user.user.id == id);
  }

  Future<void> updateUser(UserModel user) async {
    try {
      if (user.user.id.isEmpty) throw Exception('Invalid user');

      final index = _users.indexWhere((u) => u.user.id == user.user.id);
      if (index != -1) {
        _users[index] = user;
        if (user.user.id == currentUser?.user.id) {
          _currentUser.value = user;
        }
      }
    } catch (e) {
      Get.log('Error updating user: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحديث بيانات المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Update user notifications
  Future<void> updateUserNotifications(
    Map<String, dynamic> notificationsData,
  ) async {
    try {
      if (currentUser == null) throw Exception('Not logged in');

      // Create updated notifications
      final updatedNotifications = Notifications(
        push: notificationsData['push'] ?? currentUser!.user.notifications.push,
        sms: notificationsData['sms'] ?? currentUser!.user.notifications.sms,
        email:
            notificationsData['email'] ?? currentUser!.user.notifications.email,
        data: notificationsData,
      );

      // Create updated user
      final updatedUser = UserModel(
        user: User(
          id: currentUser!.user.id,
          fullName: currentUser!.user.fullName,
          email: currentUser!.user.email,
          phone: currentUser!.user.phone,
          gender: currentUser!.user.gender,
          nationality: currentUser!.user.nationality,
          dateOfBirth: currentUser!.user.dateOfBirth,
          kycStatus: currentUser!.user.kycStatus,
          verification: currentUser!.user.verification,
          preferences: currentUser!.user.preferences,
          notifications: updatedNotifications,
          account: currentUser!.user.account,
          address: currentUser!.user.address,
          documents: currentUser!.user.documents,
          contacts: currentUser!.user.contacts,
          security: currentUser!.user.security,
        ),
      );

      // Update current user
      _currentUser.value = updatedUser;

      // Update users list
      final userIndex = _users.indexWhere(
        (u) => u.user.id == currentUser!.user.id,
      );
      if (userIndex != -1) {
        _users[userIndex] = updatedUser;
      }
    } catch (e) {
      Get.log('Error updating user notifications: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'فشل في تحديث الإشعارات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  void onClose() {
    // Close stream controllers
    _transactionsStreamController.close();
    _accountStreamController.close();
    _securityStreamController.close();
    super.onClose();
  }
}
