import 'package:flutter/material.dart';
import '../../core/controllers/transaction_history_controller.dart';
import '../../core/enums/app_enums.dart';
import 'date_picker_field_widget.dart';
import 'filter_button_widget.dart';

/// Widget for displaying a filter dialog for transaction history
class FilterDialogWidget extends StatefulWidget {
  /// The controller
  final TransactionHistoryController controller;

  /// Constructor
  const FilterDialogWidget({super.key, required this.controller});

  /// Show the filter dialog
  static void show(
    BuildContext context,
    TransactionHistoryController controller,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FilterDialogWidget(controller: controller),
    );
  }

  @override
  State<FilterDialogWidget> createState() => _FilterDialogWidgetState();
}

class _FilterDialogWidgetState extends State<FilterDialogWidget> {
  late String selectedType;
  late DateTime? startDate;
  late DateTime? endDate;

  @override
  void initState() {
    super.initState();
    selectedType = widget.controller.filterType.value;
    startDate = widget.controller.startDate.value;
    endDate = widget.controller.endDate.value;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _resetFilters() {
    setState(() {
      selectedType = 'all';
      startDate = null;
      endDate = null;
    });
  }

  void _applyFilters() {
    widget.controller.filterTransactions(
      selectedType,
      startDate: startDate,
      endDate: endDate,
    );
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 16.0,
        right: 16.0,
        top: 16.0,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16.0,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildTransactionTypeDropdown(),
            const SizedBox(height: 16),
            _buildDateRangePickers(context),
            const SizedBox(height: 24),
            _buildApplyButton(),
            const SizedBox(height: 8),
            _buildResetButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('تصفية المعاملات', style: Theme.of(context).textTheme.titleLarge),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _resetFilters,
          tooltip: 'إعادة ضبط الفلاتر',
        ),
      ],
    );
  }

  Widget _buildTransactionTypeDropdown() {
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'نوع المعاملة',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.category),
      ),
      value: selectedType,
      items: [
        DropdownMenuItem(
          value: TransactionType.all.toString().split('.').last,
          child: const Text('الكل'),
        ),
        DropdownMenuItem(
          value: TransactionType.send.toString().split('.').last,
          child: const Text('مرسلة'),
        ),
        DropdownMenuItem(
          value: TransactionType.receive.toString().split('.').last,
          child: const Text('مستلمة'),
        ),
        DropdownMenuItem(
          value: TransactionType.topup.toString().split('.').last,
          child: const Text('إيداع'),
        ),
        DropdownMenuItem(
          value: TransactionType.withdraw.toString().split('.').last,
          child: const Text('سحب'),
        ),
      ],
      onChanged: (value) {
        if (value != null) {
          setState(() {
            selectedType = value;
          });
        }
      },
    );
  }

  Widget _buildDateRangePickers(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildDatePicker(
            context,
            'من تاريخ',
            startDate,
            (date) => setState(() => startDate = date),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildDatePicker(
            context,
            'إلى تاريخ',
            endDate,
            (date) => setState(() => endDate = date),
          ),
        ),
      ],
    );
  }

  Widget _buildDatePicker(
    BuildContext context,
    String label,
    DateTime? date,
    Function(DateTime?) onDateSelected,
  ) {
    return DatePickerFieldWidget(
      label: label,
      date: date,
      onDateSelected: onDateSelected,
    );
  }

  Widget _buildApplyButton() {
    return FilterButtonWidget(
      text: 'تطبيق التصفية',
      icon: Icons.filter_list,
      onPressed: _applyFilters,
      isPrimary: true,
    );
  }

  Widget _buildResetButton() {
    return FilterButtonWidget(
      text: 'إعادة ضبط الفلاتر',
      icon: Icons.clear_all,
      onPressed: () {
        widget.controller.resetFilters();
        Navigator.pop(context);
      },
      isPrimary: false,
    );
  }
}
