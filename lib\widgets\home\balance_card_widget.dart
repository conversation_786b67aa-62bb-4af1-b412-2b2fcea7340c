import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../common/widgets/custom_button/custom_button.dart';
import '../../core/controllers/home_controller.dart';
import '../../core/models/user_model.dart';
import '../../core/utils/app_formatters.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// Widget for displaying the balance card on the home screen
class BalanceCardWidget extends StatelessWidget {
  /// The user model
  final UserModel user;

  /// Constructor
  const BalanceCardWidget({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();
    final account = user.user.account;

    return Padding(
      padding: EdgeInsets.all(AppSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBalanceSection(account),
          SizedBox(height: AppSizes.md),
          _buildAccountInfoSection(account),
          SizedBox(height: AppSizes.md),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomButton(
                  label: 'Top Up',
                  onPressed: controller.navigateToTopUpWallet,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomButton(
                  label: 'Withdraw',
                  onPressed: controller.navigateToWithdrawFunds,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the balance display section
  Widget _buildBalanceSection(Account? account) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرصيد الحالي',
          style: TextStyle(
            fontSize: AppSizes.bodySmall,
            color: AppColors.greyColor,
          ),
        ),
        SizedBox(height: AppSizes.sm),
        Text(
          AppFormatters.formatCurrency(
            account?.balance ?? 0,
            currencyCode: account?.currency ?? 'USD',
          ),
          style: TextStyle(
            fontSize: AppSizes.headlineLarge.toDouble(),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build the account information section
  Widget _buildAccountInfoSection(Account? account) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildAccountInfoItem(
          'رقم الحساب',
          account?.accountNumber ?? 'غير متوفر',
        ),
        _buildAccountInfoItem('نوع الحساب', account?.type ?? 'غير متوفر'),
      ],
    );
  }

  /// Build a single account info item
  Widget _buildAccountInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppSizes.bodySmall,
            color: AppColors.greyColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: AppSizes.bodyMedium,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
