import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../core/controllers/currency_exchange_controller.dart';

class CurrencyExchangeScreen extends GetView<CurrencyExchangeController> {
  const CurrencyExchangeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحويل العملات'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildCurrencySelector(),
              const SizedBox(height: 24),
              _buildAmountInput(),
              const SizedBox(height: 24),
              _buildConvertedAmount(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrencySelector() {
    return Row(
      children: [
        Expanded(
          child: Obx(() => DropdownButtonFormField<String>(
            value: controller.fromCurrency.value,
            decoration: const InputDecoration(
              labelText: 'من',
              border: OutlineInputBorder(),
            ),
            items: controller.currencies.map<DropdownMenuItem<String>>((currency) {
              return DropdownMenuItem<String>(
                value: currency['code'] as String,
                child: Text('${currency['code']}'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                controller.fromCurrency.value = value;
                controller.updateExchangeRate();
              }
            },
          )),
        ),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.swap_horiz),
          onPressed: controller.swapCurrencies,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => DropdownButtonFormField<String>(
            value: controller.toCurrency.value,
            decoration: const InputDecoration(
              labelText: 'إلى',
              border: OutlineInputBorder(),
            ),
            items: controller.currencies.map<DropdownMenuItem<String>>((currency) {
              return DropdownMenuItem<String>(
                value: currency['code'] as String,
                child: Text('${currency['code']}'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                controller.toCurrency.value = value;
                controller.updateExchangeRate();
              }
            },
          )),
        ),
      ],
    );
  }

  Widget _buildAmountInput() {
    return TextFormField(
      controller: controller.amountController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'المبلغ',
        border: const OutlineInputBorder(),
        prefixIcon: Obx(() => Text(
          controller.currencies
              .firstWhere((c) => c['code'] == controller.fromCurrency.value)['symbol'],
          style: const TextStyle(fontSize: 20),
        )),
      ),
      onChanged: (_) => controller.updateExchangeRate(),
    );
  }

  Widget _buildConvertedAmount() {
    return Obx(() => Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المبلغ المحول',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                controller.getConvertedAmount(),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (controller.isLoading.value)
                const CircularProgressIndicator(),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'سعر الصرف: 1 ${controller.fromCurrency.value} = ${controller.exchangeRate.value} ${controller.toCurrency.value}',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    ));
  }
} 