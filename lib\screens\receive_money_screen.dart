import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../core/controllers/receive_money_controller.dart';
import 'dart:math' as math;

class ReceiveMoneyScreen extends GetView<ReceiveMoneyController> {
  const ReceiveMoneyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('استلام الأموال')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildQRCode(),
            const SizedBox(height: 24),
            _buildAmountSection(),
            const SizedBox(height: 16),
            _buildNoteSection(),
            const SizedBox(height: 24),
            _buildCurrencySelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCode() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51), // 0.2 * 255 = 51
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: AnimatedBuilder(
          animation: controller.animationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: controller.animationController.value * 2 * math.pi,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blue, width: 2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: Icon(Icons.qr_code, size: 150, color: Colors.blue),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: const Text('المبلغ'),
            trailing: IconButton(
              icon: Icon(
                controller.showAmount.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: controller.toggleAmount,
            ),
          ),
          if (controller.showAmount.value)
            TextField(
              controller: controller.amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                hintText: 'أدخل المبلغ',
                border: OutlineInputBorder(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNoteSection() {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: const Text('ملاحظة'),
            trailing: IconButton(
              icon: Icon(
                controller.showNote.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: controller.toggleNote,
            ),
          ),
          if (controller.showNote.value)
            TextField(
              controller: controller.noteController,
              decoration: const InputDecoration(
                hintText: 'أدخل ملاحظة (اختياري)',
                border: OutlineInputBorder(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCurrencySelector() {
    return Obx(
      () => DropdownButtonFormField<String>(
        value: controller.selectedCurrency.value,
        decoration: const InputDecoration(
          labelText: 'العملة',
          border: OutlineInputBorder(),
        ),
        items:
            controller.currencies.map((currency) {
              return DropdownMenuItem(value: currency, child: Text(currency));
            }).toList(),
        onChanged: (value) {
          if (value != null) {
            controller.setCurrency(value);
          }
        },
      ),
    );
  }
}
