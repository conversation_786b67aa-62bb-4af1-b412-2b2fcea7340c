import 'package:get/get.dart';
import 'package:flutter/material.dart';

class ReceiveMoney<PERSON>ontroller extends GetxController with GetSingleTickerProviderStateMixin {
  late AnimationController animationController;
  final TextEditingController amountController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  
  final RxBool showAmount = false.obs;
  final RxBool showNote = false.obs;
  final RxString selectedCurrency = 'USD'.obs;
  
  final List<String> currencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD'];

  @override
  void onInit() {
    super.onInit();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
  }

  @override
  void onClose() {
    animationController.dispose();
    amountController.dispose();
    noteController.dispose();
    super.onClose();
  }

  void toggleAmount() {
    showAmount.value = !showAmount.value;
  }

  void toggleNote() {
    showNote.value = !showNote.value;
  }

  void setCurrency(String currency) {
    selectedCurrency.value = currency;
  }
} 