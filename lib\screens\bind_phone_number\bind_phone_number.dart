import 'package:flutter/material.dart';

import '../../../common/widgets/appbar/custom_appbar.dart';
import '../../../common/widgets/custom_button/custom_button.dart';
import '../../common/widgets/text form field/costume_text_field.dart';

class BindPhoneNumber extends StatefulWidget {
  const BindPhoneNumber({super.key});

  @override
  BindPhoneNumberState createState() => BindPhoneNumberState();
}

class BindPhoneNumberState extends State<BindPhoneNumber> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationController = TextEditingController();

  void _sendVerificationCode() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Verification code has been sent to: ${_phoneController.text}',
        ),
      ),
    );
  }

  void _confirmPhoneNumber() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Phone number verified: ${_phoneController.text}'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BuildAppBar(
        showBackArrow: true,
        title: Text('Bind your phone number'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            CostumeTextField(
              prefixText: '+249',
              borderRadius: 12.0,
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              hintText: 'Phone Number',
            ),
            const SizedBox(height: 20),
            CostumeTextField(
              controller: _verificationController,
              borderRadius: 12.0,
              hintText: 'Verification Code',
              suffixIcon: TextButton(
                onPressed: _sendVerificationCode,
                child: const Text('Send'),
              ),
            ),
            const SizedBox(width: 10),
            const SizedBox(height: 20),
            CustomButton(onPressed: _confirmPhoneNumber, label: 'Confirm'),
          ],
        ),
      ),
    );
  }
}
