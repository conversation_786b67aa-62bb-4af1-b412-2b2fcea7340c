// This class contains all the App Text in String formats.
class Texts {

  // GLOBAL Texts
  static const String and = "and";
  static const String kyc = "kyc";
  static const String done = "done";
  static const String send = "Send";
  static const String receive = "Receive";
  static const String topUp = "top up";
  static const String appName = "D-FUR";
  static const String change = "change";
  static const String failed = "failed";
  static const String submit = "submit";
  static const String welcome = "welcome";
  static const String help = "help";
  static const String center = "center";
  static const String lv_1 = "LV.1";
  static const String lv_2 = "LV.2";
  static const String usd = "usd";
  static const String not = "not";
  static const String limit = "limit";
  static const String check = "check";
  static const String updates = "updates";
  static const String basic = "basic";
  static const String security = "security";
  static const String notice = "notice";
  static const String push = "push";
  static const String  method = "method";
  static const String banking = "banking";
  static const String payment = "payment";
  static const String account = "account";
  static const String preferences = "preferences";
  static const String personal = "personal";
  static const String verified = "verified";
  static const String advanced = "advanced";
  static const String settings = "settings";
  static const String tContinue = "continue";
  static const String enterprise = "enterprise";
  static const String language = "language";
  static const String transaction = "transaction";
  static const String totalBalance = "total balance";
  static const String theServicesProvidedBy = 'The services provided by BitFuFu are not available to residents (hereinafter referred to as "restricted individuals") of the following countries, including the United States, Mainland China, Crimea, Cuba, Iran, Russia, North Korea, Syria, Singapore, and Venezuela, or any entity or individual subject to restrictions under applicable trade sanctions and export laws. The list above may not be exhaustive. Before using the services provided by BitFuFu, please ensure that you are not a "restricted individual".';

  // -- OnBoarding Texts
  static const String onBoardingTitle1 = "choose your product";
  static const String onBoardingTitle2 = "select Payment Method";
  static const String onBoardingTitle3 = "Deliver at your door step";
  static const String onBoardingSubTitle3 = "From Our Doorstep to Yours - Swift, Secure, and Contactless Delivery!";
  static const String onBoardingSubTitle1 = "Welcome to a World of Limitless Choices - Your Perfect Product Awaits!";
  static const String onBoardingSubTitle2 = "For Seamless Transactions, Choose Your Payment Path - Your Convenience, Our Priority!";

  // -- Authentication Form Text
  static const String firstName = "first name";
  static const String lastName = "last name";
  static const String email = "email";
  static const String non = "N/A";
  static const String google = "google";
  static const String pin = "pin";
  static const String request = "Request";
  static const String enterEmail = "enter the email";
  static const String  password = "password";
  static const String newPassword = "new password";
  static const String enterPassword = "enter the password";
  static const String username = "username";
  static const String phone = "phone";
  static const String number = "number";
  static const String enterPhoneNumber = "enter the phone number";
  static const String rememberMe = "remember me";
  static const String forgetPassword = "forget password?";
  static const String passwordLogin = "password login";
  static const String set = "Set";
  static const String verificationCodeLogin = "verification code login";
  static const String login = "login";
  static const String logout = "log out";
  static const String signup = "sign up";
  static const String register = "register";
  static const String createAccount = "create account";
  static const String notHaveAccount = "don't have an account?";
  static const String alreadyHaveAccount = "already have an account?";
  static const String orSignInWith = "or sign in with";
  static const String orSignUpWith = "or sign up with";
  static const String iAgreeTo = "I agree to";
  static const String privacyPolicy = "privacy policy";
  static const String termsOfUse = "terms of use";
  static const String verification = "verification";
  static const String code = "code";
  static const String resend = "resend";

  // -- Authentication Headings Text
  static const String loginTitle = "Welcome";
  static const String loginSubTitle = "Send, receive and money around the world easily and fast";
  static const String signupTitle = "Let's create your account";
  static const String forgetPasswordTitle = "Forget password";
  static const String forgetPasswordSubTitle = "Don't worry sometimes people can forget too, enter your email and we will send you a password reset link.";
  static const String changeYourPasswordTitle = "Password Reset Email Sent";
  static const String changeYourPasswordSubTitle = "Your Account Security is Our Priority! We've Sent You a Secure Link to Safely Change Your Password and Keep Your Account Protected.";
  static const String confirmEmail = "Verify your email address!";
  static const String confirmEmailSubTitle = "Congratulations! Your Account Awaits: Verify Your Email to Start Shopping and Experience a World of Unrivaled Deals and Personalized Offers.";
  static const String emailNotReceivedMessage = "Didn't get the email? Check your junk/spam or resend it.";
  static const String yourAccountCreatedTitle = "Your account successfully created!";
  static const String yourAccountCreatedSubTitle = "Welcome to Your Ultimate Shopping Destination: Your Account is Created, Unleash the Joy of Seamless Online Shopping!";

  // -- Home
  static const String homeAppbarTitle = "Good day for shopping";
  static const String homeAppbarSubTitle = "Mazen Idris";

}