import 'package:flutter/material.dart';

/// Widget for displaying a setting tile with icon and tap action
class SettingTileWidget extends StatelessWidget {
  /// The title of the tile
  final String title;

  /// The icon to display
  final IconData icon;

  /// Callback when the tile is tapped
  final VoidCallback onTap;

  /// Optional subtitle text
  final String? subtitle;

  /// Constructor
  const SettingTileWidget({
    super.key,
    required this.title,
    required this.icon,
    required this.onTap,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withAlpha(25),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Theme.of(context).colorScheme.primary),
      ),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
