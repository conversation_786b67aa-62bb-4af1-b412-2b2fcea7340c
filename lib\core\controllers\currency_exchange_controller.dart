import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CurrencyExchangeController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final amountController = TextEditingController();
  final RxString fromCurrency = 'USD'.obs;
  final RxString toCurrency = 'EUR'.obs;
  final RxDouble exchangeRate = 0.85.obs;
  final RxBool isLoading = false.obs;

  final List<Map<String, dynamic>> currencies = [
    {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$'},
    {'code': 'EUR', 'name': 'Euro', 'symbol': '€'},
    {'code': 'GBP', 'name': 'British Pound', 'symbol': '£'},
    {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥'},
    {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$'},
    {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$'},
    {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'Fr'},
    {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥'},
  ];

  @override
  void onClose() {
    amountController.dispose();
    super.onClose();
  }

  void swapCurrencies() {
    final temp = fromCurrency.value;
    fromCurrency.value = toCurrency.value;
    toCurrency.value = temp;
    updateExchangeRate();
  }

  void updateExchangeRate() {
    isLoading.value = true;

    // Simulate API call to get exchange rate
    Future.delayed(const Duration(seconds: 1), () {
      isLoading.value = false;
      exchangeRate.value = getExchangeRate(fromCurrency.value, toCurrency.value);
    });
  }

  double getExchangeRate(String from, String to) {
    // Example exchange rates (in a real app, these would come from an API)
    final rates = {
      'USD': {'EUR': 0.85, 'GBP': 0.73, 'JPY': 110.0},
      'EUR': {'USD': 1.18, 'GBP': 0.86, 'JPY': 129.5},
      'GBP': {'USD': 1.37, 'EUR': 1.16, 'JPY': 150.5},
      'JPY': {'USD': 0.009, 'EUR': 0.0077, 'GBP': 0.0066},
    };

    if (from == to) return 1.0;
    return rates[from]?[to] ?? 1.0;
  }

  String getConvertedAmount() {
    if (amountController.text.isEmpty) return '0.00';
    final amount = double.tryParse(amountController.text) ?? 0.0;
    final converted = amount * exchangeRate.value;
    return NumberFormat.currency(
      symbol: currencies.firstWhere((c) => c['code'] == toCurrency.value)['symbol'],
      decimalDigits: 2,
    ).format(converted);
  }
} 