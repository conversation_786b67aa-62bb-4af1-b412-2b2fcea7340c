import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../providers/user_provider.dart';
import '../utils/app_formatters.dart';
import '../utils/status_utils.dart';
import '../utils/transaction_utils.dart';

/// Controller responsible for managing transaction details
class TransactionDetailsController extends GetxController {
  // Transaction data
  final dynamic _args = Get.arguments;
  late final Rx<Transaction?> _transaction = Rx<Transaction?>(null);

  // Transaction data is loaded from arguments or user data

  @override
  void onInit() {
    super.onInit();
    _loadTransaction();
  }

  /// Load transaction data
  void _loadTransaction() {
    try {
      // If arguments is a Map, use it directly
      if (_args is Map<String, dynamic>) {
        // Direct transaction data provided
        // No need to load from user data
        return;
      }

      // If arguments is a String, it's a transaction ID
      if (_args is String) {
        final String transactionId = _args;
        _findTransactionById(transactionId);
        return;
      }

      // If no valid arguments, show error
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على بيانات المعاملة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
      );
    } catch (e) {
      Get.log('Error loading transaction: $e', isError: true);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات المعاملة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
      );
    }
  }

  /// Find transaction by ID in user data
  void _findTransactionById(String id) {
    try {
      // Try to get user controller
      final userController = Get.find<UserController>();
      if (userController.currentUser != null) {
        final user = userController.currentUser!.user;
        if (user.account != null && user.account!.transactions.isNotEmpty) {
          // Find transaction by ID or reference
          final transaction = user.account!.transactions.firstWhere(
            (t) => t.id == id || t.reference == id,
            orElse: () => throw Exception('Transaction not found'),
          );
          _transaction.value = transaction;
        }
      }
    } catch (e) {
      Get.log('Error finding transaction: $e', isError: true);
      // Transaction not found, show error
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على المعاملة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withAlpha(180),
        colorText: Colors.white,
      );
    }
  }

  /// Get transaction data as Map
  Map<String, dynamic> get args {
    // If transaction is loaded from user data
    if (_transaction.value != null) {
      return _transaction.value!.toJson();
    }

    // If direct transaction data is provided
    if (_args is Map<String, dynamic>) {
      return _args;
    }

    // Default empty map
    return {};
  }

  /// Get transaction recipient
  String? get recipient => args['toAccount'];

  /// Get transaction amount
  double get amount => (args['amount'] ?? 0.0).toDouble();

  /// Get transaction type
  String get transactionType => args['type'] ?? '';

  /// Get transaction date
  DateTime get transactionDate {
    if (args['timestamp'] != null) {
      if (args['timestamp'] is String) {
        return DateTime.parse(args['timestamp']);
      } else if (args['timestamp'] is DateTime) {
        return args['timestamp'];
      }
    }
    return DateTime.now();
  }

  /// Get transaction note
  String get note => args['description'] ?? '';

  /// Get transaction status
  String get status => args['status'] ?? '';

  /// Get transaction reference
  String get reference => args['reference'] ?? '';

  /// Get transaction fee
  double? get fee => args['fee'] != null ? (args['fee']).toDouble() : null;

  /// Get transaction currency
  String get currency => args['currency'] ?? 'USD';

  /// Get transaction from account
  String? get fromAccount => args['fromAccount'];

  /// Get status color based on transaction status
  Color getStatusColor() {
    return StatusUtils.getTransactionStatusColor(status);
  }

  /// Get transaction type icon
  IconData getTransactionTypeIcon() {
    return TransactionUtils.getTransactionIconFromString(transactionType);
  }

  /// Get formatted date
  String getFormattedDate() {
    return AppFormatters.formatDateTime(transactionDate);
  }

  /// Get formatted amount
  String getFormattedAmount() {
    return AppFormatters.formatCurrency(amount, currencyCode: currency);
  }

  /// Get formatted fee
  String? getFormattedFee() {
    if (fee == null) return null;
    return AppFormatters.formatCurrency(fee);
  }
}
