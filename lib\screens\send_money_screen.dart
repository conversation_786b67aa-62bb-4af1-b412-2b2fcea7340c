import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../common/widgets/search/search_bar_widget.dart';
import '../core/controllers/send_money_controller.dart';
import '../core/models/user_model.dart';

class SendMoneyScreen extends GetView<SendMoneyController> {
  const SendMoneyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(
        showBackArrow: true,
        title: const Text('إرسال الأموال'),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildContactSelector(),
              const Sized<PERSON>ox(height: 24),
              _build<PERSON>mount<PERSON>ield(),
              const SizedBox(height: 24),
              _buildDescription<PERSON>ield(),
              const SizedBox(height: 32),
              _buildSendButton(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildContactSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اختر المستلم',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // Add search bar
        SearchBarWidget(
          hintText: 'البحث عن جهات الاتصال',
          onChanged: (query) => controller.searchQuery.value = query,
          padding: EdgeInsets.zero,
        ),

        const SizedBox(height: 8),

        Obx(() {
          final contacts = controller.filteredContacts;
          if (contacts.isEmpty) {
            return const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('لا توجد جهات اتصال متاحة'),
              ),
            );
          }

          return Card(
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: contacts.length,
              itemBuilder: (context, index) {
                final contact = contacts[index];
                return RadioListTile<Contact>(
                  title: Text(contact.name),
                  subtitle: Text('رقم الحساب: ${contact.account}'),
                  value: contact,
                  groupValue: controller.selectedContact.value,
                  onChanged: (value) {
                    controller.selectContact(value!);
                  },
                );
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المبلغ',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller.amountController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: 'أدخل المبلغ',
            suffixText: 'ريال',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          onChanged: controller.updateAmount,
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الوصف',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller.descriptionController,
          decoration: InputDecoration(
            hintText: 'أدخل وصفاً للتحويل',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          onChanged: controller.updateDescription,
        ),
      ],
    );
  }

  Widget _buildSendButton() {
    return ElevatedButton(
      onPressed: controller.canSend.value ? controller.sendMoney : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: const Text('إرسال', style: TextStyle(fontSize: 16)),
    );
  }
}
