import 'package:flutter/material.dart';
import 'package:owallet/utils/helpers/helpers_functions.dart';
import '../../core/enums/app_enums.dart';
import '../../core/utils/transaction_utils.dart';
import '../../utils/constants/colors.dart';

/// A unified transaction avatar widget for consistent appearance across the app
class TransactionAvatarWidget extends StatelessWidget {
  /// The transaction type
  final TransactionType transactionType;

  /// Optional size of the avatar
  final double size;

  /// Optional icon size
  final double? iconSize;

  /// Optional background color
  final Color? backgroundColor;

  /// Optional icon color
  final Color? iconColor;

  /// Constructor
  const TransactionAvatarWidget({
    super.key,
    required this.transactionType,
    this.size = 40.0,
    this.iconSize,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);

    // final bgColor =
    //     backgroundColor ??
    //     TransactionUtils.getTransactionColor(transactionType).withAlpha(30);

    // final fgColor =
    //     iconColor ?? TransactionUtils.getTransactionColor(transactionType);

    return CircleAvatar(
      radius: size / 2,
      backgroundColor:
          dark ? AppColors.darkFillColor : AppColors.lightFillColor,
      child: Icon(
        TransactionUtils.getTransactionIcon(transactionType),
        color: dark ? AppColors.darkIconsColor : AppColors.lightIconsColor,
        size: iconSize ?? (size / 2),
      ),
    );
  }
}
