import 'package:flutter/material.dart';

import '../../../utils/constants/colors.dart';
import '../../../utils/helpers/helpers_functions.dart';

class CostumeTextField extends StatelessWidget {
  const CostumeTextField({
    super.key,
    this.suffixIcon,
    this.controller,
    this.prefixText,
    this.keyboardType,
    this.onChanged,
    this.enabled = true,
    this.filled = false,
    this.contentPadding,
    required this.hintText,
    this.obscureText = false,
    this.borderRadius = 100.0,
  });

  final bool filled;
  final bool enabled;
  final bool obscureText;
  final String hintText;
  final String? prefixText;
  final Widget? suffixIcon;
  final double borderRadius;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final EdgeInsetsGeometry? contentPadding;

  @override
  Widget build(BuildContext context) {
    final dark = HelperFunctions.isDarkMode(context);
    return TextField(
      obscureText: obscureText,
      controller: controller,
      keyboardType: keyboardType,
      onChanged: onChanged,
      decoration: InputDecoration(
        hintText: hintText,
        enabled: enabled,
        filled: filled,
        prefixText: prefixText,
        contentPadding: contentPadding,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(width: 1.0, color:dark ? AppColors.darkBorderColor : AppColors.lightBorderColor)
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(width: 1.5, color:dark ? AppColors.darkBorderColor : AppColors.lightBorderColor)
        ),
        suffixIcon: suffixIcon,
      ),
    );
  }
}