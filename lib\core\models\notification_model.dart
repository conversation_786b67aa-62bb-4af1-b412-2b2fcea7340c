import 'package:flutter/material.dart';

/// Model class for notifications
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final String type;
  bool isRead;
  final String? referenceId;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.type,
    required this.isRead,
    this.referenceId,
  });

  /// Create a notification from JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      type: json['type'] ?? '',
      isRead: json['isRead'] ?? false,
      referenceId: json['referenceId'],
    );
  }

  /// Convert notification to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'isRead': isRead,
      'referenceId': referenceId,
    };
  }

  /// Get formatted time string (e.g., "2 hours ago")
  String getFormattedTime() {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة مضت';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر مضت';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضت';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }

  /// Get notification icon based on type
  IconData getIcon() {
    switch (type.toLowerCase()) {
      case 'transaction':
        return Icons.payment;
      case 'security':
        return Icons.security;
      case 'account':
        return Icons.person;
      case 'promotion':
        return Icons.local_offer;
      default:
        return Icons.notifications;
    }
  }

  /// Get notification color based on type
  Color getColor() {
    switch (type.toLowerCase()) {
      case 'transaction':
        return Colors.blue;
      case 'security':
        return Colors.red;
      case 'account':
        return Colors.green;
      case 'promotion':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
