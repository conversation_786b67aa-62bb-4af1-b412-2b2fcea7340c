import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../common/widgets/avatar_widget.dart';
import '../common/widgets/search/search_bar_widget.dart';
import '../core/controllers/contacts_controller.dart';
import '../core/models/user_model.dart';
import '../utils/constants/colors.dart';
import 'add_contact_screen.dart';
import 'edit_contact_screen.dart';

class ContactsScreen extends GetView<ContactsController> {
  const ContactsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuildAppBar(
        title: const Text('Contacts'),
        action: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Get.to(() => AddContactScreen());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          SearchBarWidget(onChanged: controller.updateSearchQuery),
          Expanded(child: _buildContactsList()),
        ],
      ),
    );
  }

  Widget _buildContactsList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final contacts = controller.filteredContacts;
      if (contacts.isEmpty) {
        return const Center(child: Text('لا توجد جهات اتصال'));
      }

      return ListView.builder(
        // padding: const EdgeInsets.all(16.0),
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final contact = contacts[index];
          return Dismissible(
            key: Key(contact.name),
            background: Container(
              color: AppColors.warning,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: const Icon(Icons.delete, color: AppColors.light),
            ),
            direction: DismissDirection.startToEnd,
            onDismissed: (direction) {
              if (direction == DismissDirection.startToEnd) {
                controller.removeContact(contact);
              }
            },
            child: ContactTile(contact: contact),
          );
        },
      );
    });
  }
}

class ContactTile extends StatelessWidget {
  final Contact contact;

  const ContactTile({super.key, required this.contact});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {},
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 0.0,
      ),
      leading: AvatarWidget(
        /// the first letter of the contact name
        letter: contact.name,
      ),
      title: Text(
        contact.name,
        style: const TextStyle(fontWeight: FontWeight.w400, fontSize: 16),
      ),
      subtitle: Text(
        'رقم الحساب: ${contact.account}',
        style: TextStyle(color: Colors.grey[600], fontSize: 14),
      ),
      trailing: PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert),
        itemBuilder:
            (context) => [
              const PopupMenuItem(value: 'edit', child: Text('تعديل')),
              const PopupMenuItem(value: 'delete', child: Text('حذف')),
            ],
        onSelected: (value) {
          if (value == 'edit') {
            Get.to(() => EditContactScreen(contact: contact));
          } else if (value == 'delete') {
            _showDeleteConfirmation(context, contact);
          }
        },
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Contact contact) {
    final controller = Get.find<ContactsController>();
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف جهة الاتصال هذه؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Get.back();
              controller.removeContact(contact);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
