import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';

class CustomButton extends StatelessWidget {
  const CustomButton(
      {super.key,
      required this.label,
      required this.onPressed,
      this.padding = const EdgeInsets.symmetric(horizontal: AppSizes.lg, vertical: AppSizes.md)});

  final String label;
  final VoidCallback onPressed;

  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: padding,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(100.0)),
      ),
      child: Center(child: Text(label)),
    );
  }
}
