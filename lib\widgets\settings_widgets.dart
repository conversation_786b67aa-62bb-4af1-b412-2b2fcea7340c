import 'package:flutter/material.dart';
import '../common/widgets/shared_widgets.dart';
import 'settings/dropdown_tile_widget.dart';
import 'settings/setting_tile_widget.dart';
import 'settings/switch_tile_widget.dart';

/// Re-export widgets for backward compatibility
export 'settings/dropdown_tile_widget.dart';
export 'settings/setting_tile_widget.dart';
export 'settings/switch_tile_widget.dart';

/// Build a section header
SectionHeaderWidget buildSectionHeader(
  String title, {
  bool showDivider = false,
}) {
  return SectionHeaderWidget(title: title, showDivider: showDivider);
}

/// Build a setting tile with icon and tap action
SettingTileWidget buildSettingTile(
  String title,
  IconData icon,
  VoidCallback onTap,
) {
  return SettingTileWidget(title: title, icon: icon, onTap: onTap);
}

/// Build a switch tile with icon
SwitchTileWidget buildSwitchTile(
  String title,
  IconData icon,
  bool value,
  ValueChanged<bool> onChanged,
) {
  return SwitchTileWidget(
    title: title,
    leadingIcon: icon,
    value: value,
    onChanged: onChanged,
  );
}

/// Build a dropdown tile with icon
DropdownTileWidget buildDropdownTile(
  String title,
  IconData icon,
  String value,
  List<String> items,
  ValueChanged<String?> onChanged,
) {
  return DropdownTileWidget(
    title: title,
    leadingIcon: icon,
    value: value,
    items: items,
    onChanged: onChanged,
  );
}
