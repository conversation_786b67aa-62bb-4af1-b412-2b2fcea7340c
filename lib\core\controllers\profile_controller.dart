import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../models/user_model.dart';

class ProfileController extends GetxController {
  late final UserController userController;
  final _isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    userController = Get.find<UserController>();
  }

  void logout() {
    userController.logout();
    Get.offAllNamed('/login');
  }

  // Getters
  RxBool get isLoading => _isLoading;

  Future<void> updateProfile(Map<String, dynamic> newProfile) async {
    try {
      _isLoading.value = true;
      final currentUser = userController.currentUser;
      if (currentUser == null) return;

      final updatedUser = UserModel(
        user: User(
          id: currentUser.user.id,
          fullName: newProfile['name'] ?? currentUser.user.fullName,
          email: newProfile['email'] ?? currentUser.user.email,
          phone: newProfile['phone'] ?? currentUser.user.phone,
          gender: currentUser.user.gender,
          nationality: currentUser.user.nationality,
          dateOfBirth: currentUser.user.dateOfBirth,
          kycStatus: currentUser.user.kycStatus,
          verification: currentUser.user.verification,
          preferences: currentUser.user.preferences,
          notifications: currentUser.user.notifications,
          account: currentUser.user.account, // This is already nullable
          address: Address(
            street:
                newProfile['address']?['street'] ??
                currentUser.user.address.street,
            city:
                newProfile['address']?['city'] ?? currentUser.user.address.city,
            state:
                newProfile['address']?['state'] ??
                currentUser.user.address.state,
            zipCode:
                newProfile['address']?['postalCode'] ??
                currentUser.user.address.zipCode,
            country:
                newProfile['address']?['country'] ??
                currentUser.user.address.country,
          ),
          documents: currentUser.user.documents,
          contacts: currentUser.user.contacts,
          security: currentUser.user.security,
        ),
      );

      await userController.updateUser(updatedUser);
      Get.snackbar(
        'نجاح',
        'تم تحديث الملف الشخصي بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث الملف الشخصي',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }
}
