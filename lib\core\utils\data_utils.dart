import '../enums/app_enums.dart';
import '../models/user_model.dart';

/// Utility class for data-related operations
class DataUtils {
  /// Sort transactions by date (newest first)
  static List<Transaction> sortTransactionsByDate(
    List<Transaction> transactions,
  ) {
    final sortedTransactions = List<Transaction>.from(transactions);
    sortedTransactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedTransactions;
  }

  /// Filter transactions by type
  static List<Transaction> filterTransactionsByType(
    List<Transaction> transactions,
    String type,
  ) {
    if (type == TransactionType.all.toString().split('.').last) {
      return transactions;
    }

    return transactions
        .where((t) => t.type.toLowerCase() == type.toLowerCase())
        .toList();
  }

  /// Filter transactions by date range
  static List<Transaction> filterTransactionsByDateRange(
    List<Transaction> transactions,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    List<Transaction> filteredTransactions = transactions;

    if (startDate != null) {
      filteredTransactions =
          filteredTransactions
              .where(
                (t) =>
                    t.timestamp.isAfter(startDate) ||
                    t.timestamp.isAtSameMomentAs(startDate),
              )
              .toList();
    }

    if (endDate != null) {
      // Add one day to include the end date fully
      final endDatePlusOne = endDate.add(const Duration(days: 1));
      filteredTransactions =
          filteredTransactions
              .where((t) => t.timestamp.isBefore(endDatePlusOne))
              .toList();
    }

    return filteredTransactions;
  }

  /// Filter transactions by account number
  static List<Transaction> filterTransactionsByAccount(
    List<Transaction> transactions,
    String accountNumber,
  ) {
    if (accountNumber.isEmpty) return transactions;

    return transactions
        .where(
          (t) =>
              (t.fromAccount != null &&
                  t.fromAccount!.contains(accountNumber)) ||
              (t.toAccount != null && t.toAccount!.contains(accountNumber)),
        )
        .toList();
  }

  /// Filter transactions by ID or reference
  static List<Transaction> filterTransactionsByIdOrReference(
    List<Transaction> transactions,
    String query,
  ) {
    if (query.isEmpty) return transactions;

    return transactions
        .where((t) => t.id.contains(query) || t.reference.contains(query))
        .toList();
  }

  /// Get recent transactions (limited by count)
  static List<Transaction> getRecentTransactions(
    List<Transaction> transactions,
    int count,
  ) {
    if (transactions.isEmpty) return [];

    final sortedTransactions = sortTransactionsByDate(transactions);
    return sortedTransactions.take(count).toList();
  }

  /// Find transaction by ID
  static Transaction? findTransactionById(
    List<Transaction> transactions,
    String id,
  ) {
    try {
      return transactions.firstWhere((t) => t.id == id || t.reference == id);
    } catch (e) {
      return null;
    }
  }

  /// Group transactions by date
  static Map<DateTime, List<Transaction>> groupTransactionsByDate(
    List<Transaction> transactions,
  ) {
    final Map<DateTime, List<Transaction>> groupedTransactions = {};

    for (final transaction in transactions) {
      final date = DateTime(
        transaction.timestamp.year,
        transaction.timestamp.month,
        transaction.timestamp.day,
      );

      if (!groupedTransactions.containsKey(date)) {
        groupedTransactions[date] = [];
      }

      groupedTransactions[date]!.add(transaction);
    }

    return groupedTransactions;
  }

  /// Calculate total amount for transactions
  static double calculateTotalAmount(List<Transaction> transactions) {
    return transactions.fold(0, (sum, transaction) => sum + transaction.amount);
  }

  /// Calculate total fees for transactions
  static double calculateTotalFees(List<Transaction> transactions) {
    return transactions.fold(
      0,
      (sum, transaction) => sum + (transaction.fee ?? 0),
    );
  }

  /// Filter transactions by search query
  static List<Transaction> filterTransactionsBySearchQuery(
    List<Transaction> transactions,
    String query,
  ) {
    if (query.isEmpty) return transactions;

    final lowerQuery = query.toLowerCase();

    return transactions.where((transaction) {
      return transaction.id.toLowerCase().contains(lowerQuery) ||
          transaction.reference.toLowerCase().contains(lowerQuery) ||
          transaction.type.toLowerCase().contains(lowerQuery) ||
          transaction.status.toLowerCase().contains(lowerQuery) ||
          transaction.description.toLowerCase().contains(lowerQuery) ||
          (transaction.fromAccount != null &&
              transaction.fromAccount!.toLowerCase().contains(lowerQuery)) ||
          (transaction.toAccount != null &&
              transaction.toAccount!.toLowerCase().contains(lowerQuery)) ||
          transaction.amount.toString().contains(lowerQuery);
    }).toList();
  }
}
