import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../common/widgets/shared_widgets.dart';
import '../../core/controllers/home_controller.dart';
import '../../core/models/user_model.dart';
import '../../core/utils/status_utils.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// Widget for displaying recent transactions section
class RecentTransactionsWidget extends StatelessWidget {
  /// The controller
  final HomeController controller;

  /// Constructor
  const RecentTransactionsWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [buildHeader(), buildTransactionsList()],
    );
  }

  /// Build the header with title and view all button
  Widget buildHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSizes.md),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المعاملات الأخيرة',
              style: TextStyle(
                fontSize: AppSizes.bodyMedium,
                fontWeight: FontWeight.normal,
                color: AppColors.lightTitleColor,
              ),
            ),
            TextButton(
              onPressed: () => Get.toNamed('/transaction-history'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the transactions list
  Widget buildTransactionsList() {
    final transactions = controller.getRecentTransactions();

    if (transactions.isEmpty) {
      return SliverToBoxAdapter(
        child: Center(
          child: Text(
            'لا توجد معاملات حديثة',
            style: TextStyle(
              color: AppColors.greyColor,
              fontSize: AppSizes.bodyMedium,
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        if (index >= transactions.length) {
          return null;
        }
        return _buildTransactionItem(transactions[index]);
      }, childCount: transactions.length),
    );
  }

  /// Build a transaction item
  Widget _buildTransactionItem(Transaction transaction) {
    return TransactionItemWidget(
      transaction: transaction,
      onTap: controller.navigateToTransactionDetails,
      getStatusColor: StatusUtils.getTransactionStatusColor,
    );
  }
}
