/*
import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../constants/sizes.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.light,
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.light,
        // Using surface instead of deprecated background
        surfaceTint: AppColors.light,
        error: AppColors.error,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        elevation: AppSizes.cardElevation,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.light,
          fontSize: AppSizes.bodyLarge,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(color: AppColors.light),
      ),
      cardTheme: CardTheme(
        elevation: AppSizes.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.light,
          padding: EdgeInsets.symmetric(
            vertical: AppSizes.md,
            horizontal: AppSizes.lg,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: AppColors.primary),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
        ),
        contentPadding: EdgeInsets.all(AppSizes.md),
        labelStyle: TextStyle(
          color: AppColors.greyColor,
          fontSize: AppSizes.bodyMedium,
        ),
        hintStyle: TextStyle(
          // Using withAlpha instead of deprecated withOpacity (0.6 * 255 = 153)
          color: AppColors.greyColor.withAlpha(153),
          fontSize: AppSizes.bodyMedium,
        ),
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: AppSizes.headlineLarge,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTitleColor,
        ),
        displayMedium: TextStyle(
          fontSize: AppSizes.headlineMedium,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTitleColor,
        ),
        displaySmall: TextStyle(
          fontSize: AppSizes.headlineSmall,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTitleColor,
        ),
        bodyLarge: TextStyle(
          fontSize: AppSizes.bodyLarge,
          color: AppColors.lightTitleColor,
        ),
        bodyMedium: TextStyle(
          fontSize: AppSizes.bodyMedium,
          color: AppColors.lightTitleColor,
        ),
        bodySmall: TextStyle(
          fontSize: AppSizes.bodySmall,
          color: AppColors.greyColor,
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.dark,
      colorScheme: ColorScheme.dark(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.darkFillColor,
        // Using surface instead of deprecated background
        surfaceTint: AppColors.dark,
        error: AppColors.error,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        elevation: AppSizes.cardElevation,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.light,
          fontSize: AppSizes.bodyLarge,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(color: AppColors.light),
      ),
      cardTheme: CardTheme(
        elevation: AppSizes.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.light,
          padding: EdgeInsets.symmetric(
            vertical: AppSizes.md,
            horizontal: AppSizes.lg,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: AppColors.primary),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
        ),
        contentPadding: EdgeInsets.all(AppSizes.md),
        labelStyle: TextStyle(
          color: AppColors.darkIconsColor,
          fontSize: AppSizes.bodyMedium,
        ),
        hintStyle: TextStyle(
          // Using withAlpha instead of deprecated withOpacity (0.6 * 255 = 153)
          color: AppColors.darkIconsColor.withAlpha(153),
          fontSize: AppSizes.bodyMedium,
        ),
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: AppSizes.headlineLarge,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTitleColor,
        ),
        displayMedium: TextStyle(
          fontSize: AppSizes.headlineMedium,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTitleColor,
        ),
        displaySmall: TextStyle(
          fontSize: AppSizes.headlineSmall,
          fontWeight: FontWeight.bold,
          color: AppColors.darkTitleColor,
        ),
        bodyLarge: TextStyle(
          fontSize: AppSizes.bodyLarge,
          color: AppColors.darkTitleColor,
        ),
        bodyMedium: TextStyle(
          fontSize: AppSizes.bodyMedium,
          color: AppColors.darkTitleColor,
        ),
        bodySmall: TextStyle(
          fontSize: AppSizes.bodySmall,
          color: AppColors.darkIconsColor,
        ),
      ),
    );
  }
}

*/