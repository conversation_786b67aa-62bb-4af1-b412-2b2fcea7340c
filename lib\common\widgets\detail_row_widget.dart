import 'package:flutter/material.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/// A unified detail row widget for displaying label-value pairs
class DetailRowWidget extends StatelessWidget {
  /// The label text
  final String label;

  /// The value text
  final String value;

  /// Optional label style
  final TextStyle? labelStyle;

  /// Optional value style
  final TextStyle? valueStyle;

  /// Optional spacing between label and value
  final double spacing;

  /// Whether to show a divider below the row
  final bool showDivider;

  /// Optional padding around the row
  final EdgeInsetsGeometry? padding;

  /// Optional alignment for the value
  final CrossAxisAlignment valueAlignment;

  /// Constructor
  const DetailRowWidget({
    super.key,
    required this.label,
    required this.value,
    this.labelStyle,
    this.valueStyle,
    this.spacing = AppSizes.sm,
    this.showDivider = false,
    this.padding,
    this.valueAlignment = CrossAxisAlignment.end,
  });

  @override
  Widget build(BuildContext context) {
    final defaultLabelStyle = TextStyle(
      fontSize: AppSizes.bodySmall,
      color: AppColors.greyColor,
    );

    final defaultValueStyle = TextStyle(
      fontSize: AppSizes.bodyMedium,
      fontWeight: FontWeight.w500,
      color: AppColors.lightTitleColor,
    );

    final content = Padding(
      padding: padding ?? EdgeInsets.symmetric(vertical: AppSizes.sm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(label, style: labelStyle ?? defaultLabelStyle),
          ),
          SizedBox(width: spacing),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: valueAlignment,
              children: [
                Text(
                  value,
                  style: valueStyle ?? defaultValueStyle,
                  textAlign: TextAlign.end,
                ),
              ],
            ),
          ),
        ],
      ),
    );

    if (showDivider) {
      return Column(children: [content, const Divider()]);
    }

    return content;
  }
}
