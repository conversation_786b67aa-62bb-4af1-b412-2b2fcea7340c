import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'utils/theme/theme.dart';
import 'utils/theme/theme_mode.dart';
import 'core/bindings/app_bindings.dart';
import 'core/controllers/settings_controller.dart';
import 'routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage
  await GetStorage.init();

  // Initialize AppBindings first
  AppBindings().dependencies();

  // Initialize SettingsController
  final settingsController = Get.find<SettingsController>();

  runApp(MyApp(settingsController: settingsController));
}

class MyApp extends StatelessWidget {
  final SettingsController settingsController;

  const MyApp({super.key, required this.settingsController});

  // Using the getThemeMode function from utils/theme/theme_mode.dart
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GetMaterialApp(
        title: 'Money Transfer App',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.light,
        darkTheme: AppTheme.dark,
        themeMode: getThemeMode(settingsController.themeMode),
        initialRoute: AppRoutes.splash,
        getPages: AppRoutes.getPages(),
        initialBinding: AppBindings(),
      ),
    );
  }
}
