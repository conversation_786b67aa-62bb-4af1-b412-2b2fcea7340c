import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/enums/app_enums.dart';
import '../../../../core/providers/user_provider.dart';
import '../../../../core/models/user_model.dart';

class AuthController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final registerFormKey = GlobalKey<FormState>();

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final isLoading = false.obs;
  final obscurePassword = true.obs;
  final obscureConfirmPassword = true.obs;

  late final UserController userController;

  @override
  void onInit() {
    super.onInit();
    userController = Get.find<UserController>();
  }

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    obscureConfirmPassword.value = !obscureConfirmPassword.value;
  }

  void login() async {
    if (formKey.currentState?.validate() ?? false) {
      isLoading.value = true;
      try {
        final success = await userController.login(
          emailController.text,
          passwordController.text,
        );

        if (success) {
          Get.offAllNamed('/home');
        } else {
          Get.snackbar(
            'خطأ',
            'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تسجيل الدخول',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }

  void register() async {
    if (registerFormKey.currentState?.validate() ?? false) {
      isLoading.value = true;
      try {
        final newUser = UserModel(
          user: User(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            fullName: nameController.text,
            email: emailController.text,
            phone: '',
            gender: '',
            nationality: '',
            dateOfBirth: '',
            kycStatus: KycStatus.pending,
            verification: Verification(
              emailVerified: false,
              phoneVerified: false,
            ),
            preferences: Preferences(
              preferredCurrency: 'USD',
              language: 'en-US',
              timezone: 'UTC',
            ),
            notifications: Notifications(push: true, sms: true, email: true),
            account: Account(
              accountNumber: DateTime.now().millisecondsSinceEpoch.toString(),
              type: 'checking',
              currency: 'USD',
              balance: 0.0,
              status: 'active',
              createdAt: DateTime.now(),
              lastLogin: DateTime.now(),
              limits: AccountLimits(
                minAmount: 10,
                maxAmount: 1000000,
                dailyTransfer: 50000,
                dailyWithdrawal: 20000,
              ),
              transactions: [],
              creditCards: [],
            ),
            address: Address(
              street: '',
              city: '',
              state: '',
              zipCode: '',
              country: '',
            ),
            documents: [],
            contacts: [],
            security: Security(
              twoFactorAuth: TwoFactorAuth(
                enabled: false,
                method: '',
                secretKey: '',
              ),
              biometricAuth: BiometricAuth(enabled: false, method: ''),
              devices: [],
            ),
          ),
        );

        await userController.updateUser(newUser);
        Get.offAllNamed('/home');
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء إنشاء الحساب',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }

  void goToLogin() {
    Get.offNamed('/login');
  }

  void goToRegister() {
    Get.offNamed('/register');
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}
