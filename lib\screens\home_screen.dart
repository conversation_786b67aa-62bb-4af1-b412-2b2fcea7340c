import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../common/widgets/appbar/custom_appbar.dart';
import '../core/controllers/home_controller.dart';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';

/// Screen for displaying home dashboard
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Obx(() {
      final user = controller.userController.currentUser;

      if (user == null) {
        return const Scaffold(body: Center(child: Text('الرجاء تسجيل الدخول')));
      }

      return Scaffold(
        appBar: BuildAppBar(
          title: const Text('Home'),
          action: [
            IconButton(
              icon: const Icon(Iconsax.user),
              onPressed: controller.navigateToAccount,
            ),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: controller.refreshData,
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    BalanceCardWidget(user: user),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        QuickActionItemWidget(
                          icon: Iconsax.send_24,
                          onPressed: controller.navigateToTransfer,
                        ),
                        QuickActionItemWidget(
                          icon: Iconsax.maximize_214,
                          onPressed: controller.navigateToTransactionHistory,
                        ),
                        QuickActionItemWidget(
                          icon: Iconsax.card,
                          onPressed: () => controller.navigateToCreditCards(),
                        ),
                        QuickActionItemWidget(
                          icon: Iconsax.document,
                          onPressed: controller.navigateToBills,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              RecentTransactionsWidget(controller: controller).buildHeader(),
              RecentTransactionsWidget(
                controller: controller,
              ).buildTransactionsList(),
            ],
          ),
        ),
      );
    });
  }
}
