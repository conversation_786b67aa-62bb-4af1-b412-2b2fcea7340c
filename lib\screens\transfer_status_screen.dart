import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../common/widgets/custom_button/custom_button.dart';
import '../utils/constants/sizes.dart';
import '../utils/constants/colors.dart';

import '../screens/transaction_history_screen.dart';

/// Controller for the transfer status screen
class TransferStatusController extends GetxController {
  // Transaction details
  final String? status;
  final String? amount;
  final String? currency;
  final String? description;

  TransferStatusController({
    this.status = 'completed',
    this.amount = '200',
    this.currency = 'USDT',
    this.description =
        'Crypto transferred out of Binance. Please contact the recipient platform for your transaction receipt.',
  });

  /// Get status color based on transaction status
  Color getStatusColor() {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
      case 'successful':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'failed':
        return AppColors.error;
      default:
        return AppColors.success;
    }
  }

  /// Navigate to transaction history screen
  void navigateToHistory() {
    Get.to(() => TransactionHistoryScreen.create());
  }
}

/// Screen that displays the status of a transfer
class TransferStatusScreen extends StatelessWidget {
  final String? status;
  final String? amount;
  final String? currency;
  final String? description;

  const TransferStatusScreen({
    super.key,
    this.status,
    this.amount,
    this.currency,
    this.description,
  });

  /// Factory constructor to create screen with default values
  static Widget create({
    String status = 'completed',
    String amount = '200',
    String currency = 'USD',
    String description =
        'Crypto transferred out of Binance. Please contact the recipient platform for your transaction receipt.',
  }) {
    return TransferStatusScreen(
      status: status,
      amount: amount,
      currency: currency,
      description: description,
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(
      TransferStatusController(
        status: status,
        amount: amount,
        currency: currency,
        description: description,
      ),
    );

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSizes.defaultSpace),
        child: Column(
          children: [
            const SizedBox(height: AppSizes.spaceBtwSections),

            // Success icon
            Container(
              padding: const EdgeInsets.all(AppSizes.xl),
              decoration: BoxDecoration(
                color: AppColors.success.withAlpha(40),
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.check, color: AppColors.success, size: 60),
            ),

            const SizedBox(height: AppSizes.spaceBtwItems),

            // Status text
            Text(
              '${controller.status == 'completed' ? 'Withdrawal' : controller.status} Successful',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w500),
            ),

            const SizedBox(height: AppSizes.spaceBtwItems),

            // Amount
            Text(
              '${controller.amount} ${controller.currency}',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 32,
              ),
            ),

            const SizedBox(height: AppSizes.spaceBtwItems),

            // Description
            Text(
              controller.description ?? '',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey,
                fontWeight: FontWeight.normal,
              ),
            ),

            const Spacer(),

            // View History button
            CustomButton(
              label: "View History",
              onPressed: controller.navigateToHistory,
            ),
            const SizedBox(height: AppSizes.defaultSpace),
          ],
        ),
      ),
    );
  }
}
